<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title></title>
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <link rel="stylesheet" href="<%= BASE_URL %>css/main.css" />
    <script>
      function getH5Url() {
        let search = window.location.search;
        let hash = window.location.hash.replace('#/', '');
        let host = window.location.host;
        let path = hash;
        let query = '';
        if (hash.indexOf('?') !== -1) {
          path = hash.split('?')[0];
          query = hash.split('?')[1];
        }
        switch (path) {
          case 'write':
            if (query.indexOf('id=') === 0 || query.indexOf('&id=') !== -1) {
              path = 'pages/write/chat'
            } else {
              path = 'pages/write/index';
            }
            break;
          case 'cosplay':
            if (query.indexOf('id=') === 0 || query.indexOf('&id=') !== -1) {
              path = 'pages/cosplay/chat'
            } else {
              path = 'pages/cosplay/index';
            }
            break;
          case 'draw':
            path = 'pages/draw/chat';
            break;
          default:
            path = '';
        }

        let h5Url = 'https://' + host + '/h5/' + search + '#/';
        if (path) {
          h5Url += path;
        }
        if (query) {
          h5Url += '?' + query;
        }

        return h5Url;
      }

      var ua = navigator.userAgent.toLowerCase();
      var isMobile = ua.match(/mobile/i);
      var isWechat = ua.match(/micromessenger/i) && !ua.match(/windows/i) && !ua.match(/macos/i) && !ua.match(/macwechat/i);
      if (isWechat || isMobile) {
        window.location.href = getH5Url();
      }
      const sitecode = window.location.search.substr(1, 4);
      var script = document.createElement('script');
      script.src = '/web.php/login/skin?sitecode=' + sitecode;
      document.head.appendChild(script);
    </script>
    <script src="/web.php/login/lang"></script>
  </head>
  <body id="body">
    <noscript>
      <strong>请允许浏览器执行javascript</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
