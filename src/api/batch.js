import request from '@/utils/request'

export function getBatchList(data) {
  return request({
    url: '/batch/getBatchList',
    method: 'post',
    data
  })
}

export function getBatchInfo(data) {
  return request({
    url: '/batch/getBatchInfo',
    method: 'post',
    data
  })
}

export function saveBatchInfo(data) {
  return request({
    url: '/batch/saveBatchInfo',
    method: 'post',
    data
  })
}

export function delBatch(data) {
  return request({
    url: '/batch/delBatch',
    method: 'post',
    data
  })
}

export function getTaskList(data) {
  return request({
    url: '/batch/getTaskList',
    method: 'post',
    data
  })
}
export function delTask(data) {
  return request({
    url: '/batch/delTask',
    method: 'post',
    data
  })
}
export function saveTask(data) {
  return request({
    url: '/batch/saveTask',
    method: 'post',
    data
  })
}
export function delAllTask(data) {
  return request({
    url: '/batch/delAllTask',
    method: 'post',
    data
  })
}
