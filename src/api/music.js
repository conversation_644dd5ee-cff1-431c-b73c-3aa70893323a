import request from '@/utils/request'

export function submitTask(data) {
  return request({
    url: '/music/submitTask',
    method: 'post',
    data
  })
}

export function getMusicSetting(data) {
  return request({
    url: '/music/getMusicSetting',
    method: 'post',
    data
  })
}

export function getMusicDetail(data) {
  return request({
    url: '/music/getMusicDetail',
    method: 'post',
    data
  })
}

export function getHistoryMsg(data) {
  return request({
    url: '/music/getHistoryMsg',
    method: 'post',
    data
  })
}

export function getPublicList(data) {
  return request({
    url: '/music/getPublicList',
    method: 'post',
    data
  })
}

export function getMusicResult(data) {
  return request({
    url: '/music/getMusicResult',
    method: 'post',
    hideLoading: true,
    data
  })
}

export function uploadMedia(data) {
  return request({
    url: '/music/uploadMedia',
    method: 'post',
    data
  })
}
