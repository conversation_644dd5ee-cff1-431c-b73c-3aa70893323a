import Vue from 'vue'
import { login, logout, getInfo, getSystemInfo } from '@/api/user'
import { getToken, setToken, removeToken, getTheme, setTheme } from '@/utils/auth'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    token: getToken(),
    user_id: 0,
    avatar: '',
    nickname: '',
    vip_expire_time: '',
    balance_point: 0,
    hasModel4: 0,
    model4Name: '高级版',
    aiList: [],
    logo: '',
    logo_mini: '',
    page_title: '',
    copyright: '',
    copyright_link: '',
    icp: '',
    gongan: '',
    theme: getTheme() || 'light',
    login_wechat: '',
    login_phone: '',
    writeIsOpen: 0,
    cosplayIsOpen: 0,
    drawIsOpen: 0,
    videoIsOpen: 0,
    musicIsOpen: 0,
    pkIsOpen: 0,
    batchIsOpen: 0,
    novelIsOpen: 0,
    teamIsOpen: 0,
    mindIsOpen: 0,
    priceSetting: {}
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USER_ID: (state, user_id) => {
    state.user_id = user_id
  },
  SET_NICKNAME: (state, nickname) => {
    state.nickname = nickname
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_VIP_EXPIRE_TIME: (state, vip_expire_time) => {
    state.vip_expire_time = vip_expire_time
  },
  SET_BALANCE_POINT: (state, balance_point) => {
    state.balance = balance_point
  },
  SET_HAS_MODEL4: (state, hasModel4) => {
    state.hasModel4 = hasModel4
  },
  SET_MODEL4_NAME: (state, model4Name) => {
    state.model4Name = model4Name
  },
  SET_AI_LIST: (state, aiList) => {
    state.aiList = aiList
  },
  SET_LOGO: (state, logo) => {
    state.logo = logo
  },
  SET_LOGO_MINI: (state, logo_mini) => {
    state.logo_mini = logo_mini
  },
  SET_PAGE_TITLE: (state, page_title) => {
    state.page_title = page_title
  },
  SET_COPYRIGHT: (state, copyright) => {
    state.copyright = copyright
  },
  SET_COPYRIGHT_LINK: (state, copyright_link) => {
    state.copyright_link = copyright_link
  },
  SET_ICP: (state, icp) => {
    state.icp = icp
  },
  SET_GONGAN: (state, gongan) => {
    state.gongan = gongan
  },
  SET_THEME: (state, theme) => {
    state.theme = theme
    setTheme(theme)
  },
  SET_LOGIN_PHONE: (state, login_phone) => {
    state.login_phone = login_phone
  },
  SET_LOGIN_WECHAT: (state, login_wechat) => {
    state.login_wechat = login_wechat
  },
  SET_WRITE_OPEN: (state, writeIsOpen) => {
    state.writeIsOpen = writeIsOpen
  },
  SET_COSPLAY_OPEN: (state, cosplayIsOpen) => {
    state.cosplayIsOpen = cosplayIsOpen
  },
  SET_DRAW_OPEN: (state, drawIsOpen) => {
    state.drawIsOpen = drawIsOpen
  },
  SET_VIDEO_OPEN: (state, videoIsOpen) => {
    state.videoIsOpen = videoIsOpen
  },
  SET_MUSIC_OPEN: (state, musicIsOpen) => {
    state.musicIsOpen = musicIsOpen
  },
  SET_PK_OPEN: (state, pkIsOpen) => {
    state.pkIsOpen = pkIsOpen
  },
  SET_BATCH_OPEN: (state, batchIsOpen) => {
    state.batchIsOpen = batchIsOpen
  },
  SET_NOVEL_OPEN: (state, novelIsOpen) => {
    state.novelIsOpen = novelIsOpen
  },
  SET_TEAM_OPEN: (state, teamIsOpen) => {
    state.teamIsOpen = teamIsOpen
  },
  SET_MIND_OPEN: (state, mindIsOpen) => {
    state.mindIsOpen = mindIsOpen
  },
  SET_PRICE_SETTING: (state, priceSetting) => {
    state.priceSetting = priceSetting
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(res => {
        const token = res.data.token
        commit('SET_TOKEN', token)
        setToken(token)
        resolve(res)
      }).catch(error => {
        console.log('error', error)
        reject(error)
      })
    })
  },

  autologin({ commit }, authcode) {
    return new Promise((resolve, reject) => {
      login({ authcode: authcode }).then(res => {
        const token = res.data.token
        commit('SET_TOKEN', token)
        setToken(token)
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response

        if (!data) {
          return reject('登录过期，请重新登录')
        }

        const { user_id, nickname, avatar, vip_expire_time, balance_point } = data

        commit('SET_USER_ID', user_id)
        commit('SET_NICKNAME', nickname)
        commit('SET_AVATAR', avatar)
        commit('SET_VIP_EXPIRE_TIME', vip_expire_time)
        commit('SET_BALANCE_POINT', balance_point)

        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  setTheme({ commit }, theme) {
    if (theme) {
      commit('SET_THEME', theme)
    }
  },

  // get user info
  getSystemInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getSystemInfo().then(response => {
        const { data } = response

        if (!data) {
          return reject('登录过期，请重新登录')
        }

        const { logo, page_title, copyright, copyright_link, icp, gongan, login_phone, login_wechat, hasModel4, model4Name, theme, writeIsOpen, cosplayIsOpen, drawIsOpen, musicIsOpen, videoIsOpen, pkIsOpen, batchIsOpen, novelIsOpen, teamIsOpen, mindIsOpen, aiList, priceSetting } = data

        commit('SET_LOGO', logo)
        commit('SET_LOGO_MINI', logo)
        commit('SET_PAGE_TITLE', page_title)
        commit('SET_COPYRIGHT', copyright)
        commit('SET_COPYRIGHT_LINK', copyright_link)
        commit('SET_ICP', icp)
        commit('SET_GONGAN', gongan)
        commit('SET_LOGIN_PHONE', login_phone)
        commit('SET_LOGIN_WECHAT', login_wechat)
        commit('SET_HAS_MODEL4', hasModel4)
        commit('SET_MODEL4_NAME', model4Name)
        commit('SET_AI_LIST', aiList)
        if (theme) {
          commit('SET_THEME', theme)
        }
        commit('SET_WRITE_OPEN', writeIsOpen)
        commit('SET_COSPLAY_OPEN', cosplayIsOpen)
        commit('SET_DRAW_OPEN', drawIsOpen)
        commit('SET_MUSIC_OPEN', musicIsOpen)
        commit('SET_VIDEO_OPEN', videoIsOpen)
        commit('SET_PK_OPEN', pkIsOpen)
        commit('SET_BATCH_OPEN', batchIsOpen)
        commit('SET_NOVEL_OPEN', novelIsOpen)
        commit('SET_TEAM_OPEN', teamIsOpen)
        commit('SET_MIND_OPEN', mindIsOpen)
        commit('SET_PRICE_SETTING', priceSetting)

        if (page_title) {
          document.title = page_title
        }

        resolve(data)
      }).catch(error => {
        if (error.errno === 403) {
          window.location.href = '/web/404'
          return
        }

        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        removeToken() // must remove  token  first
        resetRouter()
        commit('RESET_STATE')
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

