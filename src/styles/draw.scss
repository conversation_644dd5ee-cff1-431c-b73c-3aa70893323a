.draw-setting {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  border-radius: 10px;
  overflow: hidden;
  transition: background-color 0.1s ease-in-out;

  .module-body {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 80px;
    padding: 0;

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
      width: 3px;
    }

    .el-scrollbar {
      height: 100%;
    }

    .box-draw-setting {
      padding: 20px 20px;
      box-sizing: border-box;
      //transform: scale(0.88);
      .setting-row {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        .setting-item {
          width: 100%;
          padding: 10px 20px 20px 20px;
          background: #eff0f0;
          border-radius: 10px;
          &.col-1 {
            width: 220px;
          }
          &.col-2 {
            width: 350px;
          }
          &.col-3 {
            width: 480px;
          }
        }

        .header {
          font-size: 16px;
          color: #333;
          margin-bottom: 20px;
          margin-top: 15px;
          vertical-align: middle;
          letter-spacing: 1px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .tips {
            color: #999;
          }
        }
        .options {
          width: 100%;
          .option-item {
            width: 76px;
            height: 76px;
            float: left;
            margin-bottom: 8px;
            margin-right: 8px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all .2s;
            background-clip: padding-box, border-box;
            background-origin: padding-box, border-box;
            border: 3px solid transparent;
            &.active {
              background-image: linear-gradient(to right,#333333, #333333),linear-gradient(270deg,#FF5CE4 0%, #36C2FF 100%);
            }
            img {
              width: 100%;
              height: 100%;
            }
            span {
              position: absolute;
              left: 0;
              bottom: 0;
              width: 100%;
              height: 36px;
              line-height: 44px;
              white-space: nowrap;
              text-align: center;
              z-index: 9;
              color: #fff;
              font-size: 12px;
              font-weight: 500;
              background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%)
            }
          }

          textarea {
            transition: all 0.1s;
            &:focus {
              border-color: #10a37f !important;
            }
          }
          .el-upload {
            transition: all 0.1s;
            background: #fff;
            &:hover {
              border-color: #10a37f !important;
              .avatar-uploader-icon {
                color: #10a37f !important;
              }
            }
          }
        }

        .image {
          display: block;
          height: 80px;
          width: auto;
          min-width: 80px;
          max-width: 180px;
          position: relative;
          text-align: center;
          img {
            height: 80px;
            width: auto;
          }
          .del {
            display: block;
            position: absolute;
            right: 0;
            top: 0;
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            font-size: 12px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            border-bottom-left-radius: 6px;
            &:hover {
              background: #000;
            }
          }
        }

      }

      .size-item {
        width: 76px;
        height: 100px;
        float: left;
        margin-right: 10px;
        border-radius: 10px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        text-align: center;
        transition: all .2s;
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
        border: 2px solid #e2e2e2;
        padding: 10px;
        background-color: #fff;
        span {
          position: absolute;
          left:0;
          bottom: 0;
          display: block;
          width: 100%;
          height: 32px;
          line-height: 32px;
          color: #333;
          font-size: 14px;
        }

        .size-block {
          margin: 0 auto;
          background: #ddd;
          border-radius: 4px;
          transition: all .2s;
          &.size0 {
            width: 52px;
            height: 39px;
          }
          &.size1 {
            width: 39px;
            height: 52px;
          }
          &.size2 {
            width: 52px;
            height: 52px;
          }
          &.size3 {
            width: 52px;
            height: 29px;
          }
          &.size4 {
            width: 29px;
            height: 52px;
          }
        }
        &.active {
          border-color: #10a37f;
          .size-block {
            background: #10a37f;
          }
          span {
            color: #10a37f;
          }
        }
        &:last-child {
          margin-right: 0;
        }

      }
    }

  }
  .module-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;

    .box-price {
      min-width: 140px;
      height: 40px;
      background: linear-gradient(30deg, #f7debe, #f4c384);
      border-radius: 5px;
      margin: 0;
      padding: 5px 10px 5px 12px;
      box-sizing: border-box;
      color: #9a5b12;
      transition: opacity 0.1s ease-in-out;
      cursor: pointer;
      font-size: 13px;
      letter-spacing: 1px;
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;

      &:hover {
        opacity: 0.8;
      }

      .icon {
        font-size: 18px;
        margin-right: 5px;
      }
      .num {
        font-size: 16px;
        font-weight: bold;
        margin: 0 5px;
      }
      .vip {
        font-size: 14px;
        font-weight: bold;
        margin: 0 5px;
      }
    }
  }
}
.size-big {
  .sub-sidebar {
    width: 755px;
  }
  .main-wrapper {
    left: 755px;
  }
  .draw-setting {
    width: 100%;
    .module-footer {
      .box-wallet {
        width: 200px;
      }
    }
  }
  .wrapper {
    left: 755px;
  }
}
.size-small {
  width: 250px;
  .draw-setting {
    width: 100%;
    .box-draw-setting {
      width: 100%;
    }
    .module-footer {
      .box-wallet {
        width: 230px;
      }
    }
  }
  .wrapper {
    left: 265px;
  }
}

