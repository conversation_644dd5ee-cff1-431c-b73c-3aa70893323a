.main-container {
  transition: all 0.1s ease-in-out;
  padding-top: 0;
  box-sizing: border-box;
  min-width: 800px;
  position: absolute;
  left: 100px;
  right: 0;
  top: 15px;
  bottom: 15px;

  .main-wrapper {
    border-radius: 10px;
    overflow: hidden;
    position: absolute;
    left: 250px;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    padding: 0 15px;

    .ai-list {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 10px;
      .tab-ai {
        height: 36px;
        //position: absolute;
        //left: 50%;
        //top: 20px;
        //margin-left: -80px;
        border-radius: 10px;
        box-sizing: border-box;
        z-index: 99;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 0 10px rgb(0, 0, 0, 0.1);
        transition: background 0.1s ease-in-out;
        overflow: hidden;

        .tab-item {
          height: 36px;
          line-height: 36px;
          text-align: center;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          padding: 0 15px;
          &:first-child {
            border-top-left-radius: 10px;
            border-bottom-left-radius: 10px;
          }
          &:last-child {
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
          }
        }
      }
    }

    .box-msg-list {
      width: 100%;
      position: absolute;
      top: 0;
      bottom: 125px;
      left: 0;
      right: 0;
      box-sizing: border-box;
      .el-scrollbar {
        height: 100%;
        overflow-x: hidden;
      }

      .list {
        width: 100%;
        height: auto;
        padding-top: 60px;
        .row {
          width: 100%;
          transition: background 0.1s ease-in-out;
          //border-bottom: 1px solid #f2f2f2;
        }
        .row-ai {
          background: #f7f7f8;
        }
        .row-user {
          background: #fff;
        }
      }

      .message {
        width: 768px;
        margin: 0 auto;
        display: flex;
        justify-content: flex-start;
        padding: 20px 20px;
        box-sizing: border-box;
        max-width: 100%;

        &:last-child {
          border-bottom: 0;
        }
        .text {
          color: #343541;
          line-height: 28px;
          font-size: 16px;
          word-break: break-all;
          overflow: hidden;
          width: 100%;
          box-sizing: border-box;
          display: inline;
          transition: background 0.1s ease-in-out, color 0.1s ease-in-out;
          padding-top: 3px;

          .tools {
            margin-top: 15px;
            opacity: 0.3;
            transition: all 0.15s ease-in-out;
            .btn {
              width:  80px;
              height: 36px;
              cursor: pointer;
              font-size: 14px;
              margin-right: 20px;
              .icon {
                margin-right: 3px;
              }
            }
          }
          &:hover .tools{
            opacity: 1;
          }
          span {
            //display: inline;
          }

        }
        .avatar {
          width: 30px;
          height: 30px;
          min-width: 30px;
          //background: #10a37f;
          color: #fff;
          font-size: 14px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 3px;
          margin-right: 20px;
          overflow: hidden;

          img {
            width: 30px;
            height: 30px;
          }
        }
      }

    }

    .box-input {
      width: 100%;
      //background: #f7f7f8;
      position: absolute;
      bottom: 0;
      left: 0;
      padding: 20px 0 10px 0;
      box-sizing: border-box;
      transition: background 0.1s ease-in-out;

      .input {
        width: 810px;
        margin: 0 auto;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        position: relative;
        max-width: 100%;
        margin-bottom: 20px;

        .btn-send {
          position: absolute;
          right: 1px;
          bottom: 1px;
          border: none;
          height: 43px;
          transition: background 0.1s ease-in-out, color 0.1s ease-in-out;
        }
      }
      .copyright {
        width: 100%;
        height: 20px;
        font-size: 12px;
        color: #aaa;
        text-align: center;
        max-width: 100%;
        line-height: 18px;
        box-sizing: border-box;
        a:hover {
          text-decoration: underline;
        }
      }
    }

    .style-chat {
      .list {
        width: 800px;
        margin: 0 auto;
        .row-user {
          background: none;
          .message {
            display: block;
            width: 100%;
            float: right;
            padding: 15px 50px 15px 0;
            position: relative;
            .avatar {
              position: absolute;
              right: 0;
              top: 20px;
              margin-right: 0;
            }
            .text {
              //text-align: right;
              max-width: 700px;
              width: auto;
              border-radius: 10px;
              display: block;
              padding: 10px 15px;
              float: right;
            }
          }
        }
        .row-ai {
          background: none;
          .message {
            width: 100%;
            padding: 15px 0 20px 0;
            .text {
              max-width: 700px;
              width: auto;
              background: #f7f7f8;
              border-radius: 10px;
              display: block;
              padding: 10px 15px;
              float: right;
            }
          }
        }
      }
    }

    .style-write {
      .list {
        width: 800px;
        margin: 0 auto;
        .row-user {
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
          transition: border-color 0.1s ease-in-out, background 0.1s ease-in-out;
        }
        .row-ai {
          border-bottom-left-radius: 10px;
          border-bottom-right-radius: 10px;
          margin-bottom: 15px;
          transition: background 0.1s ease-in-out;
        }
      }
    }

    .style-word {
      .list {
        margin: 0 auto;
        padding: 30px 0;
        width: 100%;
        max-width: 850px;
        .row-user {
          background: none;
          .message {
            background: #fff;
            padding: 40px 50px 30px 50px;
            max-height: 156px;
            width: 100%;
            .text {
              text-align: center;
              font-size: 28px;
              line-height: 1.4;
              color: #222;
            }
          }
        }
        .row-ai {
          background: none;
          .message {
            background: #fff;
            padding: 10px 50px 20px 50px;
            min-height: 100px;
            width: 100%;
            .text {
              color: #444;
              font-size: 17px;
              .tools {
                margin-top: 30px;
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .pages {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      margin: 50px auto;
      max-width: 88%;
      span {
        display: inline-block;
        padding: 8px 15px;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 5px;
        margin: 2px 2px;
        color: #666;
        cursor: pointer;
        font-size: 16px;
        text-align: center;

        &.active, &:hover {
          background-color: #e3f5f0;
          color: #10a37f;
        }
        &.active {
          border-color: #10a37f;
        }
      }
    }

  }
}

.module-apps {
  .main-wrapper {
    //left: 0;
    //border-top-left-radius: 10px;
    //border-bottom-left-radius: 10px;
    transition: all 0.1s ease-in-out;

    .main-pk {
      width: 100%;
      height: 100%;
      max-width: 1600px;
      min-width: 1000px;
      margin: 0 auto;
      position: relative;
      .box-main {
        position: absolute;
        width: 100%;
        left: 0;
        top: 20px;
        bottom: 125px;
        overflow: hidden;
      }

      .box-msg-list {
        top: 0;
        .box-a {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 49%;
          border: 1px solid #eee;
          border-radius: 10px;
          background: #f7f7f8;
          //box-shadow: 0 0 20px rgba(0, 0, 0, 0.05)
        }
        .box-b {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 49%;
          border: 1px solid #eee;
          border-radius: 10px;
          background: #f7f7f8;
          //box-shadow: 0 0 20px rgba(0, 0, 0, 0.05)
        }
        .list {
          padding: 20px 30px;
          width: 100%;
          border-radius:10px;

          .row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            .row-ai .message .text {
              background: #eff0f0 !important;
            }
            .role-a {
              width: 50%;
              padding-right: 50px;
              box-sizing: border-box;
            }
            .role-b {
              width: 50%;
              padding-left: 50px;
              box-sizing: border-box;
            }
          }
        }
      }

      .box-input {
        display: flex;
        justify-content: space-around;
        align-items: flex-start;
        .input {
          width: 810px;
          max-width: 810px;
          min-width: 620px;
        }
        .select {
          .el-select {
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            .el-input__inner {
              background: #f7f7f8;
            }
          }
          p {
            margin: 0;
            font-size: 15px;
            color: #72787e;
            margin-bottom: 10px;
            letter-spacing: 1px;
          }
        }
      }
    }

    .main-batch {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      position: relative;
      background: #f7f7f8;
      overflow: hidden;
      border-radius: 10px;
      .el-scrollbar {
        width: 100%;
        height: 100%;
      }
      .batch-list {
        padding-bottom: 20px;
        .batch-item {
          width: 300px;
          height: 142px;
          background: #eff0f0;
          border-radius: 10px;
          float: left;
          margin: 25px 0 0 25px;
          cursor: pointer;
          overflow: hidden;
          padding: 10px 20px;
          border: 1px solid #eee;
          //box-shadow: 0 3px 3px rgba(0, 0, 0, 0.3);
          &:hover {
            background: #e8eaea;
            .footer .ops {
              display: block;
            }
          }
          .header {
            height: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .count {
              font-size: 16px;
              color: #333;
              font-weight: bold;
            }

          }
          .body {
            font-size: 14px;
            line-height: 22px;
            padding: 5px 0;
            height: 54px;
            color: #444;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 10px;
            .time {
              color: #999;
              font-size: 14px;
              display: flex;
              align-items: center;
            }
            .ops {
              display: none;
              font-size: 14px;
              i {
                margin-left: 15px;
              }
            }
          }
        }
        .btn-add {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #666;
          p {
            font-size: 17px;
          }
        }
      }

      .box-task {
        position: absolute;
        left: 20px;
        top: 0;
        right: 20px;
        bottom: 20px;
        overflow: hidden;

        .task-toolbar {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 80px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .btn-back {

          }
        }
        .task-main {
          position: absolute;
          left: 0;
          right: 0;
          top: 80px;
          bottom: 0;

          .task-list {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 550px;
            z-index: 2;
            .task-item {
              width: 100%;
              height: 48px;
              white-space: nowrap;
              overflow: hidden;
              border-top: 1px solid #e9e9e9;
              padding: 0 10px;
              display: flex;
              align-items: center;
              cursor: pointer;
              position: relative;
              &:hover, &.active {
                background: #eff0f0;
              }
              .td {
                padding: 5px 10px;
              }
              .index {
                width: 50px;
                border-radius: 5px;
                font-size: 14px;
                text-align: left;
                span {
                  //display: inline-block;
                  //width: 32px;
                  //height: 32px;
                  //line-height: 32px;
                  //font-size: 13px;
                  //background: #eff0f0;
                  //text-align: center;
                  font-size: 13px;
                  font-weight: bold;
                }
              }
              .title {
                width: 410px;
                color: #444;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .state {
                width: 70px;
              }
              .ops {
                position: absolute;
                right: 20px;
                top: 0;
                height: 48px;
                font-size: 12px;
                display: none;
                align-items: center;
                i {
                  margin: 0 10px;
                }
              }
              &:hover .ops {
                display: flex;
              }
            }
          }

          .task-chat {
            position: absolute;
            left: 549px;
            top: 0;
            bottom: 0;
            right: 0;
            background: #eff0f0;
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
            border: 1px solid #e9e9e9;
            overflow: hidden;
            width: auto;
            z-index: 1;
          }
        }

        .task-empty {
          position: absolute;
          left: 0;
          right: 0;
          top: 80px;
          bottom: 0;
          border-top: 1px solid #e9e9e9;
          display: flex;
          align-items: center;
          justify-content: center;
          .box-empty {
            width: 200px;
            height: 200px;
            margin: 0;
            text-align: center;
            .icon {
              font-size: 110px;
              color: #ddd;
              margin: 0;
              padding: 0;
            }
            .tips {
              color: #777;
              font-size: 16px;
              letter-spacing: 1px;
              margin-top: 5px;
            }
          }
        }
      }

    }

    .main-novel {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      position: relative;
      background: #f7f7f8;
      overflow: hidden;
      border-radius: 10px;
      .el-scrollbar {
        width: 100%;
        height: 100%;
      }
      .novel-list {
        padding-bottom: 20px;
        .novel-item {
          width: 300px;
          height: 142px;
          background: #eff0f0;
          border-radius: 10px;
          float: left;
          margin: 25px 0 0 25px;
          cursor: pointer;
          overflow: hidden;
          padding: 10px 20px;
          border: 1px solid #eee;
          //box-shadow: 0 3px 3px rgba(0, 0, 0, 0.3);
          &:hover {
            background: #e8eaea;
            .footer .ops {
              display: block;
            }
          }
          .title {
            height: 30px;
            padding-top: 5px;
            margin-bottom: 10px;
            overflow: hidden;
            line-height: 24px;
            font-size: 17px;
            display: flex;
            align-items: center;
            white-space: nowrap;
            .el-tag {
              margin-right: 5px;
            }
          }
          .time {
            color: #999;
            font-size: 12px;
          }
          .count {
            color: #444;
            font-size: 14px;
            margin-top: 15px;
            display: flex;
            align-items: center;
            .line {
              color: #d8d8d8;
              margin: 0 10px;
              font-size: 12px;
            }
          }
          .footer {
            font-size: 13px;
            color: #999;
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            align-content: center;
            .ops {
              display: none;
              font-size: 14px;
              i {
                margin-left: 15px;
              }
            }
          }
        }
        .btn-add {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #666;
          p {
            font-size: 17px;
          }
        }
      }

      .box-task {
        position: absolute;
        left: 20px;
        top: 0;
        right: 20px;
        bottom: 20px;
        overflow: hidden;

        .task-toolbar {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 80px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .btn-back {

          }
        }
        .task-main {
          position: absolute;
          left: 0;
          right: 0;
          top: 80px;
          bottom: 0;

          .task-list {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 550px;
            z-index: 2;
            .task-item {
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              border-top: 1px solid #e9e9e9;
              padding: 8px 10px;
              height: 66px;
              display: flex;
              align-items: flex-start;
              cursor: pointer;
              position: relative;
              p {
                margin: 0;
              }
              .subtitle {
                font-size: 13px;
                color: #aaa;
                white-space: nowrap;
                margin-top: 5px;
                overflow: hidden;
                text-overflow: ellipsis;
                padding-right: 20px;
              }
              &:hover, &.active {
                background: #eff0f0;
              }
              .td {
                padding: 5px 10px;
              }
              .index {
                width: 50px;
                border-radius: 5px;
                font-size: 14px;
                text-align: left;
                span {
                  //display: inline-block;
                  //width: 32px;
                  //height: 32px;
                  //line-height: 32px;
                  //font-size: 13px;
                  //background: #eff0f0;
                  //text-align: center;
                  font-size: 13px;
                  font-weight: bold;
                }
              }
              .title {
                width: 410px;
                color: #444;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .state {
                width: 70px;
              }
              .ops {
                position: absolute;
                right: 20px;
                top: 0;
                height: 48px;
                font-size: 12px;
                display: none;
                align-items: center;
                i {
                  margin: 0 10px;
                }
              }
              &:hover .ops {
                display: flex;
              }
            }
          }

          .task-chat {
            position: absolute;
            left: 549px;
            top: 0;
            bottom: 0;
            right: 0;
            background: #eff0f0;
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
            border: 1px solid #e9e9e9;
            overflow: hidden;
            width: auto;
            z-index: 1;
            .overview {
              width: 100%;
              background: #f7f9f9;
              padding: 10px 20px;
              color: #909090;
              line-height: 1.4;
              font-size: 14px;
            }
          }
        }

        .task-empty {
          position: absolute;
          left: 0;
          right: 0;
          top: 80px;
          bottom: 0;
          border-top: 1px solid #e9e9e9;
          display: flex;
          align-items: center;
          justify-content: center;
          .box-empty {
            width: 200px;
            height: 200px;
            margin: 0;
            text-align: center;
            .icon {
              font-size: 110px;
              color: #ddd;
              margin: 0;
              padding: 0;
            }
            .tips {
              color: #777;
              font-size: 16px;
              letter-spacing: 1px;
              margin-top: 5px;
            }
          }
        }
      }

    }

  }
}
