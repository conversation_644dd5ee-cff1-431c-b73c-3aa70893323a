@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './sub-sidebar.scss';
@import './main.scss';
@import './draw.scss';
@import './video.scss';
@import './music.scss';
@import './theme-light.scss';
@import './theme-dark.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 0 20px;
  border: 1px solid #eee;
  margin: 15px;
}

.toolbar {
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.bg-gray {
  background-color: #f2f4f6 !important;
}

.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  transition: background-color 0.1s ease-in-out;
}
