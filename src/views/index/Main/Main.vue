<template>
  <div class="main-wrapper">
    <div class="ai-list">
      <div class="tab-ai">
        <div v-for="(item, index) in aiList" class="tab-item" :class="{active: ai.name === item.name, gold: isGpt4(item.name)}" @click="switchAi(index)">
          {{ item.alias }}
        </div>
      </div>
    </div>
    <div v-if="lists && lists.length > 0" class="box-msg-list" :class="{'style-chat': module === 'chat' || module === 'cosplay', 'style-write': module === 'write' || module === 'draw'}">
      <el-scrollbar ref="msglist" wrap-class="scrollbar-wrapper">
        <div class="list">
          <div v-for="(item, index) in lists" class="row" :class="item.user === 'AI'? 'row-ai' : 'row-user'">
            <div v-if="item.user === 'AI'" class="message">
              <div class="avatar">
                <img :src="aiAvatar">
              </div>
              <div class="text markdown-body">
                <textComponent
                  :id="'message-' + index"
                  :text="item.message"
                />
                <div class="tools">
                  <span class="btn text-primary" @click="copyText(index)"><svg-icon class="icon" icon-class="ic_copy" />{{ '复制内容' | lang }}</span>
                  <span v-if="lists.length > 1" class="btn text-primary" :title="'导出Word' | lang" @click="exportDocx(index)"><svg-icon class="icon" icon-class="ic_word" />{{ '导出Word' | lang }}</span>
                  <span v-if="(module === 'write' && writePrompt.is_ppt && lists.length > 1) || isPPT(index)" class="btn text-primary" :title="'生成PPT' | lang" @click="pptExport(index)"><svg-icon class="icon" icon-class="ic_ppt" />{{ '生成PPT' | lang }}</span>
                  <span v-if="(module === 'write' && writePrompt.is_table && lists.length > 1) || isTable(index)" class="btn text-primary" :title="'下载Excel' | lang" @click="tableExport(index)"><svg-icon class="icon" icon-class="ic_excel" />{{ '下载Excel' | lang }}</span>
                  <div style="float: right;">
                    <!--<span class="btn text-primary" :title="'生成思维导图' | lang"><svg-icon class="icon" icon-class="ic_xmind"></svg-icon></span>-->
                    <span v-if="lists.length > 1" class="btn text-primary" :title="'重新回答' | lang" style="margin-right: 0;" @click="retry(index - 1)"><svg-icon class="icon" icon-class="ic_retry" /></span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="message">
              <div class="avatar" style="background: #9aa37e;">
                <img :src="avatar || '/static/img/ic_user.png'">
              </div>
              <div class="text markdown-body" v-html="textToHtml(item.message)" />
            </div>
            <div style="clear: both" />
          </div>
          <div v-if="writing || writingText" class="row row-ai">
            <div class="message">
              <div class="avatar"><img :src="aiAvatar"></div>
              <div class="text markdown-body">
                <textComponent
                  :text="writingText"
                  :writing="!!(writing || writingText)"
                />
                <div class="tools">
                  <span
                    class="btn text-primary"
                    @click="stopFetch()"
                  ><svg-icon class="icon" icon-class="ic_stop" />{{ '停止回复' | lang }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <WelcomeChat
      v-else
      :module="module"
      :title="welcomeTitle"
      :desc="welcomeDesc"
      :tips="welcomeTips"
      :has-model4="!!hasModel4"
      @use="quickMessage"
    />

    <div class="box-input">
      <div class="input">
        <el-input
          ref="messageInput"
          v-model="message"
          :class="{gold: isGpt4(ai.name)}"
          :placeholder="placeHolder | lang"
          type="textarea"
          :autofocus="true"
          :autosize="{ minRows: 2, maxRows: 8 }"
          @keyup.enter.native="onEnter"
        />
        <el-button
          class="btn-send"
          type="default"
          :loading="writing"
          :title="'发送' | lang"
          @click="sendText"
        >
          <i v-if="!writing" class="el-icon-position" /> 发送
        </el-button>
      </div>

      <Copyright />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getHistoryMsg, getChatSetting } from '@/api/chat'
import { getPrompt } from '@/api/write'
import { getRole } from '@/api/cosplay'
import { getToken, getSiteCode, setStorage, getStorage } from '@/utils/auth'
import { toDoc } from '@/utils/util'
import { TextComponent, WelcomeChat, Copyright } from '@/views/components'
import htmlDocx from 'html-docx-js/dist/html-docx'
import fileSaver from 'file-saver'

import 'katex/dist/katex.min.css'
import '@/styles/lib/tailwind.css'
import '@/styles/lib/highlight.scss'
import '@/styles/lib/github-markdown.scss'

var autoFocusSi = 0
var textStacks = []
var textOutputSi = 0
var fetchCtrl = null

export default {
  name: 'Main',
  components: { TextComponent, WelcomeChat, Copyright },
  data() {
    return {
      module: 'chat',
      ai: {},
      group_id: 0,
      lists: [],
      message: '',
      writingText: '',
      writing: false,
      page: 1,
      pagesize: 10,
      prompt_id: 0,
      chatSetting: {},
      writePrompt: {},
      role_id: 0,
      cosplayRole: {}
    }
  },
  computed: {
    ...mapGetters([
      'avatar',
      'nickname',
      'page_title',
      'hasModel4',
      'model4Name',
      'aiList',
      'priceSetting'
    ]),
    aiAvatar() {
      var avatar = '/static/img/ic_ai.png'
      if (this.module === 'cosplay') {
        avatar = this.cosplayRole.thumb || '/static/img/avatar_cosplay.png'
      }
      return avatar
    },
    placeHolder() {
      var placeholder = '输入您的问题，Ctrl+回车发送'
      if (this.module === 'write') {
        if (this.writePrompt && this.writePrompt.hint) {
          placeholder = this.writePrompt.hint
        }
      } else if (this.module === 'cosplay') {
        if (this.cosplayRole && this.cosplayRole.hint) {
          placeholder = this.cosplayRole.hint
        }
      }
      return placeholder
    },
    welcomeTitle() {
      if (this.module === 'chat') {
        return this.page_title
      } else if (this.module === 'write') {
        return this.writePrompt.title
      } else if (this.module === 'cosplay') {
        return this.cosplayRole.title
      }
      return ''
    },
    welcomeDesc() {
      let desc = ''
      if (this.module === 'chat') {
        if (!this.chatSetting.welcome && !this.chatSetting.tips) {
          desc = this.$lang('请输入你的问题')
        } else {
          desc = this.chatSetting.welcome
        }
      } else if (this.module === 'write') {
        desc = this.writePrompt.welcome ? this.writePrompt.welcome : this.writePrompt.hint
      } else if (this.module === 'cosplay') {
        desc = this.cosplayRole.welcome ? this.cosplayRole.welcome : this.cosplayRole.hint
      }
      return desc
    },
    welcomeTips() {
      if (this.module === 'chat') {
        return this.chatSetting.tips
      } else if (this.module === 'write') {
        return this.writePrompt.tips
      } else if (this.module === 'cosplay') {
        return this.cosplayRole.tips
      }
      return ''
    }
  },
  watch: {
    message() {
      setStorage('chatPrompt', this.message)
    }
  },
  mounted() {
    this.startAutoFocus()
    // miclink input
    window.handlInputValue = this.miclinkInput
    this.getChatSetting()
    // 自动填充内容
    if (this.$route.query.prompt) {
      this.message = decodeURIComponent(this.$route.query.prompt)
    } else {
      var prompt = getStorage('chatPrompt')
      if (prompt) {
        this.message = prompt
      }
    }
  },
  destroyed() {
    if (autoFocusSi) {
      clearInterval(autoFocusSi)
    }
  },
  methods: {
    async sendText() {
      if (this.writing || this.writingText) {
        return
      }
      const message = this.trim(this.message)
      if (!message) {
        this.$message.error('请输入您的问题')
        this.message = ''
        return
      }

      if (textOutputSi) {
        clearInterval(textOutputSi)
        textOutputSi = 0
        textStacks = []
      }

      this.lists.push({
        user: '我',
        message: message
      })
      this.message = ''
      this.writing = true
      this.scrollBottom()

      try {
        const headers = new Headers()
        headers.append('Content-Type', 'application/json')
        headers.append('X-token', getToken() || '')
        headers.append('X-site', getSiteCode() || '')
        const url = process.env.VUE_APP_BASE_API + '/chat/sendText'
        const data = {
          message: message,
          ai: this.ai.name
        }
        if (this.module === 'chat') {
          data.group_id = this.group_id
        } else if (this.module === 'write') {
          data.prompt_id = this.prompt_id
        } else if (this.module === 'cosplay') {
          data.role_id = this.role_id
        }
        fetchCtrl = new AbortController()
        const response = await fetch(url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(data),
          signal: fetchCtrl.signal
        })

        if (!response.ok) {
          this.writing = false
          this.$message.error(response.statusText)
          return
        }
        const reader = response.body.getReader()
        const decoder = new TextDecoder('utf-8')
        let done = false
        const curAiMsg = ''

        // eslint-disable-next-line require-atomic-updates
        textOutputSi = setInterval(() => {
          if (this.writing) {
            if (textStacks.length > 0) {
              this.writingText += textStacks.shift()
              this.scrollBottom()
            }
          } else {
            if (textStacks.length > 0) {
              this.writingText += textStacks.join('')
              textStacks = []
              this.scrollBottom()
            }
            clearInterval(textOutputSi)
            textOutputSi = 0
            if (this.writingText) {
              this.lists.push({
                user: 'AI',
                message: this.writingText
              })
            }
            this.writingText = ''
            this.scrollBottom()
          }
        }, 25)

        while (!done) {
          this.scrollBottom()
          const { value, done: readerDone } = await reader.read()
          if (value) {
            const char = decoder.decode(value)
            if (char === '\n' && curAiMsg.endsWith('\n')) {
              continue
            }
            if (char) {
              if (char.indexOf('[error]') !== -1) {
                this.writing = false
                this.writingText = ''
                this.lists.pop()
                this.message = message
                const error = char.replace('[error]', '')
                if (error.indexOf('请登录') !== -1) {
                  this.$emit('showLogin')
                  setTimeout(() => {
                    this.$message.error(this.$lang(error))
                  }, 500)
                } else if (error.indexOf('请充值') !== -1) {
                  if (this.isGpt4(this.ai.name)) {
                    if (this.priceSetting.text40_vip) {
                      this.$emit('showPay', 'vip')
                    } else {
                      this.$emit('showPay', 'point')
                    }
                  } else {
                    this.$emit('showPay', 'vip')
                  }
                  setTimeout(() => {
                    this.$message.error(this.$lang(error))
                  }, 500)
                } else {
                  this.$alert(error, '系统提示')
                }
                break
              }
              this.writing = true
              for (var i = 0; i < char.length; i++) {
                textStacks.push(char[i])
              }
            }
          }
          done = readerDone
        }
        this.writing = false
      } catch (e) {
        e = e.toString()
        if (e.indexOf('user aborted') !== -1) {
          console.log('已终止连接')
        } else {
          console.log('Request Error', e)
        }
      }
    },
    miclinkInput(value = '', flag = 0) {
      if (value) {
        this.message = value
      }
      if (flag === 2 || flag === 3) {
        this.sendText()
      }
    },
    switchAi(index) {
      const ai = this.aiList[index]
      if (this.ai.name === ai.name) {
        this.scrollTop += 1
        return
      }
      if (this.writing) {
        this.$message.warning(this.$lang('输出中，请稍等'))
        return
      }
      if (this.isGpt4(ai.name)) {
        this.ai = ai
        this.page = 1
        this.getHistoryMsg()
        this.$message.success('已切换至【' + ai.alias + '】')
      } else {
        this.ai = ai
        this.page = 1
        this.getHistoryMsg()
        this.$message.success('已切换至【' + ai.alias + '】')
      }
      if (this.ai.name) {
        setStorage('ai', this.ai.name)
      }
      this.$refs.messageInput.focus()
    },
    setGroupId(group_id) {
      this.module = 'chat'
      this.group_id = group_id
      this.page = 1
      this.setDefaultAi()
      this.getHistoryMsg()
    },
    setPromptId(prompt_id) {
      this.module = 'write'
      this.prompt_id = prompt_id
      if (this.prompt_id) {
        this.setDefaultAi()
        if (!this.$route.query.id || parseInt(this.$route.query.id) !== parseInt(this.prompt_id)) {
          this.$router.replace({
            query: Object.assign({}, this.$route.query ? this.$route.query : {}, { id: this.prompt_id })
          }).catch(err => {})
        }
        setStorage('prompt_id', this.prompt_id)
        this.page = 1
        this.getPrompt()
        this.getHistoryMsg()
      }
    },
    setRoleId(role_id) {
      this.module = 'cosplay'
      this.role_id = role_id
      if (this.role_id) {
        this.setDefaultAi()
        if (!this.$route.query.id || parseInt(this.$route.query.id) !== parseInt(this.role_id)) {
          this.$router.replace({
            query: Object.assign({}, this.$route.query ? this.$route.query : {}, { id: this.role_id })
          }).catch(err => {})
        }
        setStorage('role_id', this.role_id)
        this.page = 1
        this.getRole()
        this.getHistoryMsg()
      }
    },
    setDefaultAi() {
      if (!this.ai.name && this.aiList && this.aiList.length > 0) {
        let ai = this.aiList[0]
        // 本地存储的上次用的AI名字
        const lastAI = getStorage('ai')
        if (lastAI) {
          this.aiList.forEach(item => {
            if (item.name === lastAI) {
              ai = item
            }
          })
        }
        this.ai = ai
      }
    },
    startAutoFocus() {
      autoFocusSi = setInterval(() => {
        const selection = window.getSelection ? window.getSelection() : document.selection.createRange().text
        if (selection.anchorNode) {
          const className = selection.anchorNode.className
          if (className && (className.indexOf('el-input') !== -1 || className.indexOf('el-textarea') !== -1)) {
            return
          }
        }
        if (!selection.toString() && this.$refs.messageInput) {
          this.$refs.messageInput.focus()
        }
      }, 1000)
    },
    getHistoryMsg() {
      const data = {
        ai: this.ai.name,
        page: this.page,
        pagesize: this.pagesize
      }
      if (this.module === 'chat') {
        data.group_id = this.group_id
      } else if (this.module === 'write') {
        data.prompt_id = this.prompt_id
      } else if (this.module === 'cosplay') {
        data.role_id = this.role_id
      }
      getHistoryMsg(data).then(res => {
        this.lists = res.data.list
        this.$nextTick(() => {
          this.scrollBottom()
        })
      })
    },
    getPrompt() {
      getPrompt({
        prompt_id: this.prompt_id
      }).then(res => {
        this.writePrompt = res.data
      })
    },
    getChatSetting() {
      getChatSetting().then(res => {
        this.chatSetting = res.data
      })
    },
    getRole() {
      getRole({
        role_id: this.role_id
      }).then(res => {
        this.cosplayRole = res.data
      })
    },
    quickMessage(text) {
      this.message = text
    },
    copyText(index) {
      const _this = this
      const content = this.lists[index].message
      _this.$copyText(content).then(
        function() {
          _this.$message.success(_this.$lang('已复制'))
        },
        function() {
          _this.$message.error(_this.$lang('复制失败，请重试'))
        }
      )
    },
    onEnter(e) {
      if (e.ctrlKey) {
        this.sendText()
      }
      return false
    },
    retry(index) {
      this.message = this.lists[index].message
      this.sendText()
    },
    stopFetch() {
      const writingText = this.writingText
      this.writing = false
      fetchCtrl.abort()
      setTimeout(() => {
        if (!writingText) {
          this.lists.pop()
        }
      }, 50)
    },
    trim(str) {
      if (str) {
        str = str.replace(/(^\s*)|(\s*$)/g, '')
      }
      return str
    },
    textToHtml(str) {
      str = this.trim(str)
      if (str && typeof str.replaceAll === 'function') {
        str = str.replaceAll('  ', '&nbsp;&nbsp;').replaceAll('<', '&lt;').replaceAll('>', '&gt;').replaceAll('\n', '<br>')
      }
      return str
    },
    scrollBottom() {
      this.$nextTick(() => {
        if (this.$refs['msglist']) {
          const container = this.$refs['msglist'].wrap
          setTimeout(() => {
            container.scrollTop = container.scrollHeight
          }, 200)
        }
      })
    },
    toDoc(type) {
      toDoc(type)
    },
    hasMarkdown(index) {
      const content = this.lists[index].message
      const regex = /^# |```/
      return regex.test(content) && content.indexOf('\n') !== -1
    },
    isTable(index) {
      if (!this.lists[index - 1]) {
        return false
      }
      const question = this.lists[index - 1].message
      const regex = /(做|写|建|生成|制|创).*?表/ig
      const regex2 = /表.*?输出/ig
      return regex.test(question) || regex2.test(question)
    },
    isPPT(index) {
      if (!this.lists[index - 1]) {
        return false
      }
      const question = this.lists[index - 1].message
      const regex = /(做|生成|写|制作|提供|输出).*?PPT|ppt/ig
      return regex.test(question)
    },
    exportDocx(index) {
      const html = '<div style="font-size: 12pt;">' + this.textToHtml(this.lists[index].message) + '</div>'
      const contentBlob = htmlDocx.asBlob(html)
      fileSaver.saveAs(contentBlob, 'word.docx')
    },
    pptExport(index) {
      var content = this.lists[index].message
      var start = content.indexOf('\n\n```markdown')
      if (content.indexOf('#') !== 0) {
        if (start !== -1) {
          content = content.substr(start + '\n\n```markdown'.length + 1)
          start = content.indexOf('```\n\n')
          if (start !== -1) {
            content = content.substr(0, start)
          }
        } else {
          start = content.indexOf('\n\n```')
          if (start !== -1) {
            content = content.substr(start + '\n\n```'.length + 1)
            start = content.indexOf('```\n\n')
            if (start !== -1) {
              content = content.substr(0, start)
            }
          } else {
            start = content.indexOf('\n\n#')
            if (start !== -1) {
              content = content.substr(start + '\n\n'.length)
              start = this.markdownEndSearch(content, '\n\n')
              if (start != -1) {
                content = content.substr(0, start)
              }
            }
          }
        }
      }
      const url =	'https://mindshow.fun/#/home?channel=miclink&markdown=' + encodeURIComponent(content)
      window.open(url, '_blank')
    },
    tableExport(index) {
      const domMessage = document.getElementById('message-' + index)
      const domTable = domMessage.getElementsByTagName('table')[0]
      if (!domTable) {
        this.$message.error('非table格式，无法导出')
        return
      }
      const content = domTable.outerHTML
      const fileName = 'table.xls'
      const sheetName = 'Sheet1'
      try {
        const template = '\ufeff<html xmlns:o="urn:schemas-microsoft-com:office:office"  xmlns:x="urn:schemas-microsoft-com:office:excel"  xmlns="http://www.w3.org/TR/REC-html40"> <head> <!--[if gte mso 9]> <xml> <x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet> <x:Name>' + sheetName + '</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml> <![endif]--></head><body>' + content + '</body></html>'
        const url = 'data:application/vnd.ms-excel;base64,' + window.btoa(unescape(encodeURIComponent(template)))
        this.downloadFile(url, fileName)
      } catch (err) {
        this.$message.error('导出失败')
      }
    },
    markdownEndSearch(text, regexp, length = 0) {
      if (!(regexp instanceof RegExp)) regexp = new RegExp(regexp)
      let e = -1
      for (let i = text.length - length - 1; i >= 0; i--) {
        const l = text.substring(i, length > 0 ? i + length : text.length)
        if (l.search(regexp) === 0) {
          e = i
          break
        }
      }
      return e
    },
    downloadFile(url, fileName) {
      try {
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        return true
      } catch (err) {
        console.error(err)
        return false
      }
    },
    isGpt4(name) {
      return ['openai4', 'diy42', 'diy43', 'azure_openai4', 'wenxin4', 'hunyuan4', 'zhipu4', 'claude2'].includes(name)
    }
  }
}
</script>
<style lang="scss">
.scrollbar-wrapper {
  height: 100%;
  overflow-x: hidden;
}
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.input .el-textarea__inner {
  padding: 10px 72px 10px 15px;
  letter-spacing: 1px;
  transition: all 0.1s ease-in-out;
  font-size: 16px;
}

</style>

<style lang="scss">
.markdown-body {
  display: block;
  width: 100%;
  background-color: transparent;
  font-size: 14px;
  box-sizing: border-box;

  p {
    white-space: pre-wrap;
    text-align: left;
    &:last-child {
      margin-bottom: 0;
    }
  }

  img[alt=cursor] {
    width: 5px;
    height: 20px;
    position: relative;
    top: 4px;
    left: 2px;
  }

  ol {
    list-style-type: decimal;
  }

  ul {
    list-style-type: disc;
  }
  /**代码行号**/
  .code-linenum {
    text-align: right;
    float: left;
    padding: 0;
    margin: 0 1em 0 0;
  }
  .code-linenum-line {
    list-style: none;
    margin: 0;
    color: #ccc;
    transition: color 0.1s ease-in-out;
  }

  pre code,
  pre tt {
    line-height: 1.6;
    font-size: 16px;
  }
  pre code img[alt=cursor] {
    display: none;
  }

  .highlight pre,
  pre {
    //background-color: #fff;
    background-color: #edeff2;
    margin-top: 16px;
    transition: background 0.1s ease-in-out;
    text-align: left;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #b3b3b3;

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;

        &:hover {
          color: #65a665;
        }
      }
    }
  }

  .code-block-wrapper {
    display: block;
    width: 100%
  }

}
</style>
