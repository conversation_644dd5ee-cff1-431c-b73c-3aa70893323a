<template>
  <div class="main-novel">
    <el-scrollbar v-if="!activeNovel" wrap-class="scrollbar-wrapper">
      <div class="novel-list">
        <div v-if="page === 1" class="novel-item btn-add" @click="showNovelForm('')">
          <i class="el-icon-plus" style="font-size: 48px;" />
          <div style="margin-top: 15px;">创建新作品</div>
        </div>
        <div v-for="item in novelList" class="novel-item" @click="openNovelConsole(item.novel_id)">
          <div class="title">
            <el-tag v-if="item.count_finished === 0" type="info" size="mini">未开始</el-tag>
            <el-tag v-else-if="item.count_finished < item.count_total" type="warning" size="mini">未完成</el-tag>
            <el-tag v-else-if="item.count_finished >= item.count_total" type="success" size="mini">已完成</el-tag>
            <span>{{ item.title }}</span>
          </div>
          <div class="time">{{ item.create_time }} 创建</div>
          <div class="count">
            <span>章节 {{ item.count_finished }} / {{ item.count_total }}</span>
            <span class="line">|</span>
            <span>已写 {{ item.words }} 字</span>
          </div>
          <div class="footer">
            <span>ID：{{ item.novel_id }}</span>
            <div class="ops">
              <i class="el-icon-edit" title="编辑" @click.stop="showNovelForm(item.novel_id)" />
              <i class="el-icon-delete text-danger" title="删除" @click.stop="delNovel(item.novel_id)" />
            </div>
          </div>
        </div>
        <div style="clear: both;" />
      </div>
      <pages :page="page" :page-total="pageTotal" @change="changePage" />
    </el-scrollbar>
    <novel-task v-else :novel_id="activeNovel.novel_id" :ai="activeNovel.ai" @close="closeNovelConsole" @showNovelForm="showNovelForm" @showLogin="showLogin" @showPay="showPay" />
    <div v-if="novelForm">
      <el-dialog
        custom-class="my-dialog"
        :title="novelForm.novel_id ? '作品设置' : '创建新作品' | lang"
        width="780px"
        :visible="true"
        :close-on-click-modal="false"
        :append-to-body="true"
        :before-close="closeNovelForm"
      >
        <el-form ref="form" :model="novelForm" label-width="100px" style="padding: 20px 0;">
          <el-form-item :label="'作品名称' | lang">
            <el-input v-model="novelForm.title" :placeholder="'请输入作品名称' | lang" size="normal" style="width: 400px;" />
          </el-form-item>
          <el-form-item :label="'AI通道' | lang">
            <el-select v-model="novelForm.ai" placeholder="请选择">
              <el-option
                v-for="item in aiList"
                :label="item.alias"
                :value="item.name"
              />
            </el-select>
            <div v-if="isGpt4(novelForm.ai)" class="text-primary">
              <p v-if="priceSetting.text40 > 0" style="line-height: 24px; margin: 5px 0; font-size: 16px;">高级通道，每章节消耗 {{ priceSetting.text40 }} {{ priceSetting.title }}<span v-if="priceSetting.text40_vip">（会员免费）</span></p>
              <p v-else style="line-height: 24px; margin: 5px 0; font-size: 16px;">高级通道，免费使用</p>
            </div>
            <div v-else class="text-primary">
              <p v-if="priceSetting.text35 > 0" style="line-height: 24px; margin: 5px 0; font-size: 16px;">每章节消耗 {{ priceSetting.text35 }} {{ priceSetting.title }}<span v-if="priceSetting.text35_vip">（会员免费）</span></p>
              <p v-else style="line-height: 24px; margin: 5px 0; font-size: 16px;">免费使用</p>
            </div>
          </el-form-item>
          <el-form-item :label="'写作要求' | lang">
            <el-input v-model="novelForm.prompt" type="textarea" :rows="4" :placeholder="'输入您对作品的要求' | lang" size="normal" style="width: 580px;" />
          </el-form-item><!--
          <el-form-item :label="'作品概要' | lang">
            <el-input type="textarea" :rows="6" v-model="novelForm.overview" :placeholder="'简要描述作品的大概内容' | lang" size="normal" style="width: 660px;" />
          </el-form-item>-->
          <el-form-item :label="'作品大纲' | lang">
            <el-input v-model="novelForm.sketch" type="textarea" :rows="10" :placeholder="'作品大纲尽量详细，建议字数不超过3000' | lang" size="normal" style="width: 580px;" />
            <p>
              <a v-if="writing" class="text-primary" style="cursor: pointer; display: flex; align-items: center; width: 100px;" @click="stopFetch">
                <svg-icon icon-class="ic_stop" style="font-size: 20px;" />
                <span style="font-size: 16px; margin-left: 5px;">停止生成</span>
              </a>
              <a v-else class="text-primary" style="cursor: pointer; display: flex; align-items: center; width: 100px;" @click="showMakeSketch">
                <svg-icon icon-class="ic_write" style="font-size: 20px;" />
                <span style="font-size: 16px; margin-left: 5px;">生成大纲</span>
              </a>
            </p>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="default" icon="el-icon-close" size="small" @click="closeNovelForm">{{ '取 消' | lang }}</el-button>
          <el-button type="primary" icon="el-icon-check" size="small" @click="saveNovelInfo">{{ '确 定' | lang }}</el-button>
        </span>
      </el-dialog>
      <div v-if="makeSketchShow">
        <el-dialog
          custom-class="my-dialog"
          :title="'生成大纲' | lang"
          width="620px"
          :visible="true"
          :close-on-click-modal="false"
          :append-to-body="true"
          :before-close="closeMakeSketch"
        >
          <el-form ref="form" :model="novelForm" label-width="100px" style="padding: 20px 0;">
            <el-form-item :label="'作品概要' | lang">
              <el-input id="sketch" v-model="novelForm.overview" type="textarea" :rows="8" :placeholder="'简要描述作品的大概内容' | lang" size="normal" style="width: 430px;" />
              <div v-if="isGpt4(novelForm.ai)" class="text-primary">
                <p v-if="priceSetting.text40 > 0" style="line-height: 24px; margin: 5px 0; font-size: 16px;">消耗 {{ priceSetting.text40 }} {{ priceSetting.title }}<span v-if="priceSetting.text40_vip">（会员免费）</span></p>
              </div>
              <div v-else class="text-primary">
                <p v-if="priceSetting.text35 > 0" style="line-height: 24px; margin: 5px 0; font-size: 16px;">消耗 {{ priceSetting.text35 }} {{ priceSetting.title }}<span v-if="priceSetting.text35_vip">（会员免费）</span></p>
              </div>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="default" icon="el-icon-close" size="small" @click="closeMakeSketch">{{ '取 消' | lang }}</el-button>
            <el-button type="primary" icon="el-icon-check" size="small" @click="makeSketch">{{ '开始生成' | lang }}</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getNovelList, getNovelInfo, saveNovelInfo, delNovel } from '@/api/novel'
import novelTask from '@/views/index/components/novelTask'
import pages from '@/views/components/Pages'
import { getSiteCode, getToken } from '@/utils/auth'

var textStacks = []
var textOutputSi = 0
var fetchCtrl = null
export default {
  name: 'Novel',
  components: { novelTask, pages },
  data() {
    return {
      activeNovel: null,
      novelList: [],
      novelForm: null,
      page: 1,
      pagesize: 39,
      novelTotal: 0,
      makeSketchShow: false,
      writing: false
    }
  },
  computed: {
    ...mapGetters([
      'aiList',
      'priceSetting'
    ]),
    pageTotal() {
      return Math.ceil(this.novelTotal / this.pagesize)
    }
  },
  watch: {
    '$route.query.novel_id'() {
      if (!this.$route.query.novel_id) {
        this.activeNovel = null
      } else {
        this.getNovelInfo(this.$route.query.novel_id)
      }
    }
  },
  mounted() {
    this.getNovelList()
    if (this.$route.query.novel_id) {
      this.getNovelInfo(this.$route.query.novel_id)
    }
  },
  methods: {
    showMakeSketch() {
      this.makeSketchShow = true
    },
    closeMakeSketch() {
      this.makeSketchShow = false
    },
    async makeSketch() {
      if (this.writing || this.writingText) {
        return
      }
      const loadingText = '正在生成...'
      const sketch = this.novelForm.sketch
      let message = this.novelForm.overview
      const ai = this.novelForm.ai
      if (!message) {
        this.$message.error('请输入作品概要')
        return
      }
      if (!ai) {
        this.$message.error('请选择AI通道')
        return
      }
      this.novelForm.sketch = loadingText
      document.getElementById('sketch').focus()
      if (this.novelForm.title) {
        message = '作品名称：' + this.novelForm.title + '\n' + message
      }

      if (textOutputSi) {
        clearInterval(textOutputSi)
        textOutputSi = 0
        textStacks = []
      }

      this.writing = true
      this.scrollSketchBottom()
      this.closeMakeSketch()

      const headers = new Headers()
      headers.append('Content-Type', 'application/json')
      headers.append('X-token', getToken() || '')
      headers.append('X-site', getSiteCode() || '')
      const url = process.env.VUE_APP_BASE_API + '/chat/sendText'
      const data = {
        tool: 'sketch',
        message: message,
        ai: ai
      }
      fetchCtrl = new AbortController()
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        signal: fetchCtrl.signal
      })
      if (!response.ok) {
        this.writing = false
        this.$message.error(response.statusText)
        return
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let done = false
      const curAiMsg = ''

      // eslint-disable-next-line require-atomic-updates
      textOutputSi = setInterval(() => {
        if (this.writing) {
          if (textStacks.length > 0) {
            if (this.novelForm.sketch === loadingText) {
              this.novelForm.sketch = ''
            }
            this.novelForm.sketch += textStacks.shift()
            this.scrollSketchBottom()
          }
        } else {
          if (textStacks.length > 0) {
            if (this.novelForm.sketch === loadingText) {
              this.novelForm.sketch = ''
            }
            this.novelForm.sketch += textStacks.join('')
            textStacks = []
            this.scrollSketchBottom()
          }
          clearInterval(textOutputSi)
          textOutputSi = 0
          this.scrollSketchBottom()
        }
      }, 25)

      while (!done) {
        this.scrollSketchBottom()
        const { value, done: readerDone } = await reader.read()
        if (value) {
          const char = decoder.decode(value)
          if (char === '\n' && curAiMsg.endsWith('\n')) {
            continue
          }
          if (char) {
            if (char.indexOf('[error]') !== -1) {
              this.writing = false
              this.novelForm.sketch = sketch
              const error = char.replace('[error]', '')
              if (error.indexOf('请登录') !== -1) {
                this.$emit('showLogin')
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else if (error.indexOf('请充值') !== -1) {
                if (this.isGpt4(this.ai)) {
                  if (this.priceSetting.text40_vip) {
                    this.showPay('vip')
                  } else {
                    this.showPay('point')
                  }
                } else {
                  this.$emit('showPay', 'vip')
                }
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else {
                this.$alert(error, '系统提示')
              }
              break
            }
            this.writing = true
            for (var i = 0; i < char.length; i++) {
              textStacks.push(char[i])
            }
          }
        }
        done = readerDone
      }
      this.writing = false
    },
    scrollSketchBottom() {
      this.$nextTick(() => {
        // const sketch = document.getElementById('sketch')
        // sketch.scrollTop = sketch.scrollHeight
      })
    },
    stopFetch() {
      this.writing = false
      fetchCtrl.abort()
    },
    getNovelList() {
      getNovelList({
        page: this.page,
        pagesize: this.pagesize
      }).then(res => {
        this.novelList = res.data.list
        this.novelTotal = res.data.count
      }).catch(err => {
        if (err.errno === 403) {
          this.showLogin()
        }
      })
    },
    changePage(page) {
      if (this.page !== page) {
        this.page = page
        this.getNovelList()
      }
    },
    showNovelForm(novel_id = '') {
      if (novel_id) {
        getNovelInfo({
          novel_id: novel_id
        }).then(res => {
          this.novelForm = {
            novel_id: novel_id,
            ai: res.data.ai,
            title: res.data.title,
            prompt: res.data.prompt,
            overview: res.data.overview,
            sketch: res.data.sketch
          }
        }).catch(err => {
          if (err.errno === 403) {
            this.showLogin()
          }
        })
      } else {
        this.novelForm = {
          ai: this.aiList.length > 0 ? this.aiList[0].name : '',
          title: '',
          prompt: '',
          overflow: '',
          sketch: ''
        }
      }
    },
    getNovelInfo(novel_id) {
      getNovelInfo({
        novel_id: novel_id
      }).then(res => {
        this.activeNovel = res.data
      }).catch(err => {
        if (err.errno === 403) {
          this.showLogin()
        }
      })
    },
    closeNovelForm() {
      this.novelForm = null
    },
    delNovel(novel_id) {
      this.$confirm('删除后不可恢复，确认删除吗?', '提示', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delNovel({
          novel_id: novel_id
        }).then(res => {
          this.getNovelList()
        }).catch(err => {
          if (err.errno === 403) {
            this.showLogin()
          }
        })
      })
    },
    saveNovelInfo() {
      if (!this.novelForm.ai) {
        this.$message.error('请选择AI通道')
        return
      }
      saveNovelInfo(this.novelForm).then(res => {
        this.$message.success(res.message)
        if (this.activeNovel) {
          this.$set(this.activeNovel, 'ai', this.novelForm.ai)
        }
        this.getNovelList()
        this.closeNovelForm()
      }).catch(err => {
        if (err.errno === 403) {
          this.showLogin()
        }
      })
    },
    openNovelConsole(novel_id) {
      this.$router.push({
        query: { novel_id: novel_id }
      }).catch(err => {})
    },
    closeNovelConsole(novel_id) {
      this.$router.push({
        name: 'novel'
      }).catch(err => {})
    },
    showLogin() {
      this.$emit('showLogin')
    },
    showPay(type) {
      this.$emit('showPay', type)
    },
    isGpt4(name) {
      return ['openai4', 'diy42', 'diy43', 'azure_openai4', 'wenxin4', 'hunyuan4', 'zhipu4', 'claude2'].includes(name)
    }
  }
}
</script>

<style lang="scss">
.scrollbar-wrapper {
  height: 100%;
  overflow-x: hidden;
}
</style>
