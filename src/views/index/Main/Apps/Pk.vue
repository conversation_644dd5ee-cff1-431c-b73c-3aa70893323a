<template>
  <div class="main-pk">
    <div class="box-msg-list style-chat">
      <div class="box-a" />
      <div class="box-b" />
      <el-scrollbar ref="msglist" wrap-class="scrollbar-wrapper">
        <div v-if="lists && lists.length > 0" class="list">
          <div v-for="(item, index) in lists" class="row">
            <div class="role-a">
              <div class="row-user">
                <div class="message">
                  <div class="avatar" style="background: #9aa37e;">
                    <img :src="avatar || '/static/img/ic_user.png'">
                  </div>
                  <div class="text markdown-body" v-html="textFormat(item.message)" />
                </div>
              </div>
              <div v-if="item.response_a" class="row-ai">
                <div class="message">
                  <div class="avatar"><img :src="aiAvatar"></div>
                  <div class="text markdown-body">
                    <textComponent
                      :id="'message-' + index"
                      :text="item.response_a"
                    />
                    <div class="tools">
                      <span class="btn text-primary" @click="copyText(item.response_a)"><svg-icon class="icon" icon-class="ic_copy" />{{ '复制内容' | lang }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="role-b">
              <div class="row-user">
                <div class="message">
                  <div class="avatar" style="background: #9aa37e;">
                    <img :src="avatar || '/static/img/ic_user.png'">
                  </div>
                  <div class="text markdown-body" v-html="textFormat(item.message)" />
                </div>
              </div>
              <div v-if="item.response_b" class="row-ai">
                <div class="message">
                  <div class="avatar">
                    <img :src="aiAvatar">
                  </div>
                  <div class="text markdown-body">
                    <textComponent
                      :id="'message-' + index"
                      :text="item.response_b"
                    />
                    <div class="tools">
                      <span class="btn text-primary" @click="copyText(item.response_b)"><svg-icon class="icon" icon-class="ic_copy" />{{ '复制内容' | lang }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="writingA || writingTextA || writingB || writingTextB" class="row">
            <div class="role-a">
              <div v-if="writingA || writingTextA" class="row-ai">
                <div class="message">
                  <div class="avatar"><img :src="aiAvatar"></div>
                  <div class="text markdown-body">
                    <textComponent
                      :text="writingTextA"
                      :writing="writingA"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="role-b">
              <div v-if="writingB || writingTextB" class="row-ai">
                <div class="message">
                  <div class="avatar"><img :src="aiAvatar"></div>
                  <div class="text markdown-body">
                    <textComponent
                      :text="writingTextB"
                      :writing="writingB"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="list">
          <div class="row">
            <div class="role-a">
              <div class="row-ai">
                <div class="message">
                  <div class="avatar"><img :src="aiAvatar"></div>
                  <div class="text markdown-body">
                    请选择一个模型，开始你的提问
                  </div>
                </div>
              </div>
            </div>
            <div class="role-b">
              <div class="row-ai">
                <div class="message">
                  <div class="avatar"><img :src="aiAvatar"></div>
                  <div class="text markdown-body">
                    请选择一个模型，开始你的提问
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="box-input">
      <div class="select">
        <p>选择左侧AI</p>
        <el-select v-model="aiA" placeholder="请选择" @change="switchAi_A">
          <el-option
            v-for="item in aiList"
            :label="item.alias"
            :value="item.name"
          />
        </el-select>
      </div>
      <div class="input">
        <el-input
          ref="messageInput"
          v-model="message"
          :placeholder="'输入您的问题，Ctrl+回车发送' | lang"
          type="textarea"
          :autofocus="true"
          :autosize="{ minRows: 2, maxRows: 8 }"
          @keyup.enter.native="onEnter"
        />
        <el-button
          class="btn-send"
          type="default"
          :loading="writingA || writingB"
          :title="'发送' | lang"
          @click="sendText"
        >
          <i v-if="!writingA && !writingB" class="el-icon-position" /> 发送
        </el-button>
      </div>
      <div class="select">
        <p style="text-align: right;">选择右侧AI</p>
        <el-select v-model="aiB" placeholder="请选择" @change="switchAi_B">
          <el-option
            v-for="item in aiList"
            :label="item.alias"
            :value="item.name"
          />
        </el-select>
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getPkHistoryMsg, createPk } from '@/api/chat'
import { getToken, getSiteCode, setStorage, getStorage } from '@/utils/auth'
import { TextComponent } from '@/views/components'

import 'katex/dist/katex.min.css'
import '@/styles/lib/tailwind.css'
import '@/styles/lib/highlight.scss'
import '@/styles/lib/github-markdown.scss'

var autoFocusSi = 0
var textStacksA = []
var textOutputSiA = 0
var fetchCtrlA = null
var textStacksB = []
var textOutputSiB = 0
var fetchCtrlB = null

export default {
  name: 'Pk',
  components: { TextComponent },
  data() {
    return {
      message: '',
      aiA: '',
      lists: [],
      writingTextA: '',
      writingA: false,
      aiB: '',
      writingTextB: '',
      writingB: false,
      channelA: '',
      channelB: ''
    }
  },
  computed: {
    ...mapGetters([
      'avatar',
      'nickname',
      'aiList'
    ]),
    aiAvatar() {
      return '/static/img/ic_ai.png'
    }
  },
  mounted() {
    this.startAutoFocus()
    // miclink input
    window.handlInputValue = this.miclinkInput
    this.getHistoryMsg()
    const ai_a = getStorage('pk_ai_a')
    const ai_b = getStorage('pk_ai_b')
    if (ai_a) {
      this.aiA = ai_a
    }
    if (ai_b) {
      this.aiB = ai_b
    }
  },
  destroyed() {
    if (autoFocusSi) {
      clearInterval(autoFocusSi)
    }
  },
  methods: {
    sendText() {
      if (this.writing || this.writingText) {
        return
      }

      if (!this.aiA || !this.aiB) {
        this.$message.error('请先选择模型')
        return
      }
      const message = this.trim(this.message)
      if (!message) {
        this.$message.error('请输入您的问题')
        this.message = ''
        return
      }
      createPk({
        message: message,
        channel_a: this.aiA,
        channel_b: this.aiB
      }).then(res => {
        const pk_id = res.data.pk_id
        this.lists.push({
          id: pk_id,
          channel_a: this.aiA,
          channel_b: this.aiB,
          message: message,
          response_a: '',
          response_b: ''
        })
        this.sendTextA(pk_id, message)
        this.sendTextB(pk_id, message)
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    async sendTextA(pk_id, message) {
      if (textOutputSiA) {
        clearInterval(textOutputSiA)
        textOutputSiA = 0
        textStacksA = []
      }

      this.message = ''
      this.writingA = true
      this.scrollBottom()

      const headers = new Headers()
      headers.append('Content-Type', 'application/json')
      headers.append('X-token', getToken() || '')
      headers.append('X-site', getSiteCode() || '')
      const url = process.env.VUE_APP_BASE_API + '/chat/sendText'
      const data = {
        message: message,
        ai: this.aiA,
        pk_id: pk_id,
        pk_role: 'a'
      }
      fetchCtrlA = new AbortController()
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        signal: fetchCtrlA.signal
      })
      if (!response.ok) {
        this.writingA = false
        this.$message.error(response.statusText)
        return
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let done = false
      const curAiMsg = ''

      // eslint-disable-next-line require-atomic-updates
      textOutputSiA = setInterval(() => {
        if (this.writingA) {
          if (textStacksA.length > 0) {
            this.writingTextA += textStacksA.shift()
            this.scrollBottom()
          }
        } else {
          if (textStacksA.length > 0) {
            this.writingTextA += textStacksA.join('')
            textStacksA = []
            this.scrollBottom()
          }
          if (!this.writingB) {
            clearInterval(textOutputSiA)
            textOutputSiA = 0
            if (this.writingTextA) {
              this.lists[this.lists.length - 1].response_a = this.writingTextA
            }
            this.writingTextA = ''
            this.$forceUpdate()
          }
          this.scrollBottom()
        }
      }, 25)

      while (!done) {
        this.scrollBottom()
        const { value, done: readerDone } = await reader.read()
        if (value) {
          const char = decoder.decode(value)
          if (char === '\n' && curAiMsg.endsWith('\n')) {
            continue
          }
          if (char) {
            if (char.indexOf('[error]') !== -1) {
              this.writingA = false
              this.writingTextA = ''
              this.message = message
              const error = char.replace('[error]', '')
              if (error.indexOf('请登录') !== -1) {
                this.$emit('showLogin')
                setTimeout(() => {
                  this.$message.closeAll()
                  this.$message.error(this.$lang(error))
                }, 500)
              } else if (error.indexOf('请充值') !== -1) {
                this.$message.closeAll()
                this.$emit('showPay', 'vip')
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else {
                this.$alert(error, '系统提示')
              }
              break
            }
            this.writingA = true
            for (var i = 0; i < char.length; i++) {
              textStacksA.push(char[i])
            }
          }
        }
        done = readerDone
      }
      this.writingA = false
    },
    async sendTextB(pk_id, message) {
      if (textOutputSiB) {
        clearInterval(textOutputSiB)
        textOutputSiB = 0
        textStacksB = []
      }

      this.message = ''
      this.writingB = true
      this.scrollBottom()

      const headers = new Headers()
      headers.append('Content-Type', 'application/json')
      headers.append('X-token', getToken() || '')
      headers.append('X-site', getSiteCode() || '')
      const url = process.env.VUE_APP_BASE_API + '/chat/sendText'
      const data = {
        message: message,
        ai: this.aiB,
        pk_id: pk_id,
        pk_role: 'b'
      }
      fetchCtrlB = new AbortController()
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        signal: fetchCtrlB.signal
      })
      if (!response.ok) {
        this.writingB = false
        this.$message.error(response.statusText)
        return
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let done = false
      const curAiMsg = ''

      // eslint-disable-next-line require-atomic-updates
      textOutputSiB = setInterval(() => {
        if (this.writingB) {
          if (textStacksB.length > 0) {
            this.writingTextB += textStacksB.shift()
            this.scrollBottom()
          }
        } else {
          if (textStacksB.length > 0) {
            this.writingTextB += textStacksB.join('')
            textStacksB = []
            this.scrollBottom()
          }
          if (!this.writingA) {
            clearInterval(textOutputSiB)
            textOutputSiB = 0
            if (this.writingTextB) {
              this.lists[this.lists.length - 1].response_b = this.writingTextB
            }
            this.writingTextB = ''
            this.$forceUpdate()
          }
          this.scrollBottom()
        }
      }, 25)

      while (!done) {
        this.scrollBottom()
        const { value, done: readerDone } = await reader.read()
        if (value) {
          const char = decoder.decode(value)
          if (char === '\n' && curAiMsg.endsWith('\n')) {
            continue
          }
          if (char) {
            if (char.indexOf('[error]') !== -1) {
              this.writingB = false
              this.writingTextB = ''
              this.message = message
              const error = char.replace('[error]', '')
              if (error.indexOf('请登录') !== -1) {
                this.$emit('showLogin')
                setTimeout(() => {
                  this.$message.closeAll()
                  this.$message.error(this.$lang(error))
                }, 500)
              } else if (error.indexOf('请充值') !== -1) {
                this.$emit('showPay', 'vip')
                setTimeout(() => {
                  this.$message.closeAll()
                  this.$message.error(this.$lang(error))
                }, 500)
              } else {
                this.$alert(error, '系统提示')
              }
              break
            }
            this.writingB = true
            for (var i = 0; i < char.length; i++) {
              textStacksB.push(char[i])
            }
          }
        }
        done = readerDone
      }
      this.writingB = false
    },
    miclinkInput(value = '', flag = 0) {
      if (value) {
        this.message = value
      }
      if (flag === 2 || flag === 3) {
        this.sendText()
      }
    },
    switchAi_A(name) {
      this.switchAi('a', name)
    },
    switchAi_B(name) {
      this.switchAi('b', name)
    },
    switchAi(role, name) {
      let alias = name
      this.aiList.forEach(item => {
        if (item.name === name) {
          alias = item.alias
        }
      })
      if (this.isGpt4(name)) {
        this.$message.success((role === 'a' ? '左侧' : '右侧') + '已切换至【' + alias + '】')
      } else {
        this.$message.success((role === 'a' ? '左侧' : '右侧') + '已切换至【' + alias + '】')
      }
      if (role === 'a') {
        setStorage('pk_ai_a', name)
      } else {
        setStorage('pk_ai_b', name)
      }
      this.$refs.messageInput.focus()
    },
    setDefaultAi() {
      if (!this.ai.name && this.aiList && this.aiList.length > 0) {
        let ai = this.aiList[0]
        // 本地存储的上次用的AI名字
        const lastAI = getStorage('ai')
        if (lastAI) {
          this.aiList.forEach(item => {
            if (item.name === lastAI) {
              ai = item
            }
          })
        }
        this.ai = ai
      }
    },
    startAutoFocus() {
      autoFocusSi = setInterval(() => {
        const selection = window.getSelection ? window.getSelection() : document.selection.createRange().text
        if (selection.anchorNode) {
          const className = selection.anchorNode.className
          if (className && (className.indexOf('el-input') !== -1 || className.indexOf('el-textarea') !== -1)) {
            return
          }
        }
        if (!selection.toString() && this.$refs.messageInput) {
          this.$refs.messageInput.focus()
        }
      }, 1000)
    },
    getHistoryMsg() {
      const data = {
        page: this.page,
        pagesize: this.pagesize
      }
      getPkHistoryMsg(data).then(res => {
        this.lists = res.data.list
        this.$nextTick(() => {
          this.scrollBottom()
        })
      })
    },

    copyText(content) {
      const _this = this
      _this.$copyText(content).then(
        function() {
          _this.$message.success(_this.$lang('已复制'))
        },
        function() {
          _this.$message.error(_this.$lang('复制失败，请重试'))
        }
      )
    },
    onEnter(e) {
      if (e.ctrlKey) {
        this.sendText()
      }
      return false
    },
    stopFetch() {
      fetchCtrlA.abort()
      fetchCtrlB.abort()
      this.writingA = false
      this.writingB = false
      setTimeout(() => {
        if (this.writingTextA) {
          this.lists[this.lists.length - 1].response_a = this.writingTextA
        }
        this.writingTextA = ''

        if (this.writingTextB) {
          this.lists[this.lists.length - 1].response_b = this.writingTextB
        }
        this.writingTextB = ''
      }, 50)
    },
    trim(str) {
      if (str) {
        str = str.replace(/(^\s*)|(\s*$)/g, '')
      }
      return str
    },
    textFormat(str) {
      str = this.trim(str)
      if (str && typeof str.replaceAll === 'function') {
        str = str.replaceAll(' ', '&nbsp').replaceAll('<', '&lt;').replaceAll('>', '&gt;').replaceAll('\n', '<br>')
      }
      return str
    },
    scrollBottom() {
      this.$nextTick(() => {
        if (this.$refs['msglist']) {
          const container = this.$refs['msglist'].wrap
          setTimeout(() => {
            container.scrollTop = container.scrollHeight
          }, 200)
        }
      })
    },
    isGpt4(name) {
      return ['openai4', 'diy42', 'diy43', 'azure_openai4', 'wenxin4', 'hunyuan4', 'zhipu4', 'claude2'].includes(name)
    }
  }
}
</script>

<style lang="scss">
.scrollbar-wrapper {
  height: 100%;
  overflow-x: hidden;
}
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.input .el-textarea__inner {
  padding: 10px 72px 10px 15px;
  letter-spacing: 1px;
  transition: all 0.1s ease-in-out;
  font-size: 16px;
}
</style>

<style lang="scss">
.markdown-body {
  display: block;
  width: 100%;
  background-color: transparent;
  font-size: 14px;
  box-sizing: border-box;

  p {
    white-space: pre-wrap;
    text-align: left;
    &:last-child {
      margin-bottom: 0;
    }
  }

  img[alt=cursor] {
    width: 5px;
    height: 20px;
    position: relative;
    top: 4px;
    left: 2px;
  }

  ol {
    list-style-type: decimal;
  }

  ul {
    list-style-type: disc;
  }
  /**代码行号**/
  .code-linenum {
    text-align: right;
    float: left;
    padding: 0;
    margin: 0 1em 0 0;
  }
  .code-linenum-line {
    list-style: none;
    margin: 0;
    color: #ccc;
    transition: color 0.1s ease-in-out;
  }

  pre code,
  pre tt {
    line-height: 1.6;
    font-size: 16px;
  }
  pre code img[alt=cursor] {
    display: none;
  }

  .highlight pre,
  pre {
    //background-color: #fff;
    background-color: #edeff2;
    margin-top: 16px;
    transition: background 0.1s ease-in-out;
    text-align: left;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #b3b3b3;

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;

        &:hover {
          color: #65a665;
        }
      }
    }
  }

  .code-block-wrapper {
    display: block;
    width: 100%
  }

}
</style>
