<template>
  <div class="size-big">
    <div v-if="drawSetting['channel']" class="sub-sidebar">
      <div class="draw-setting">
        <div class="module-body">
          <drawYijian
            v-if="drawSetting['channel'] === 'yijian'"
            ref="drawSetting"
            :settings="drawSetting['options']"
            @showLogin="showLogin"
            @showPay="showPay"
          />
          <drawMj
            v-if="drawSetting['platform'] === 'mj'"
            ref="drawSetting"
            @showLogin="showLogin"
            @showPay="showPay"
          />
          <drawOpenai
            v-if="drawSetting['platform'] === 'openai'"
            ref="drawSetting"
            @showPay="showPay"
          />
        </div>
        <div class="module-footer">
          <div v-if="priceSetting.draw > 0" class="box-price" @click="showPay">
            <svg-icon class="icon" icon-class="ic_mod_draw" />
            <span class="num">{{ priceSetting.draw }}</span>{{ priceSetting.title }}/{{ '次' | lang }}
            <span v-if="priceSetting.draw_vip" class="vip">会员免费</span>
          </div>
          <div v-else class="box-price" style="cursor: default;">
            <svg-icon class="icon" icon-class="ic_mod_draw" />
            <span class="vip">免费使用</span>
          </div>
          <div style="display: flex; align-items: center;">
            <el-select v-model="cate_id" placeholder="请选择图片分类" class="cate" size="normal" style="margin-right: 20px; width: 180px;">
              <el-option
                v-for="item in drawCate"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
            <el-checkbox v-model="is_share" label="共享到图库" />
          </div>
          <el-button
            class="btn-draw"
            type="primary"
            @click="submitTask"
          >
            <i class="el-icon-position" /> 开始生成图片
          </el-button>
        </div>
      </div>
    </div>

    <div class="main-wrapper">
      <div v-if="lists && lists.length > 0" class="box-msg-list style-write" style="bottom: 0;">
        <el-scrollbar ref="msglist" wrap-class="scrollbar-wrapper">
          <div class="list" style="padding-top: 0; width: 658px;">
            <div v-for="(item, index) in lists" class="row">
              <div class="message row-user" style="padding: 25px 30px 15px 30px;">
                <div class="avatar" style="background: #9aa37e;">
                  <img :src="avatar || '/static/img/ic_user.png'">
                </div>
                <div class="text markdown-body" style="padding-top: 3px;">
                  {{ item.message }}
                  <div class="tools" style="opacity: 1; margin-top: 10px !important;">
                    <span
                      class="btn text-primary"
                      title="将参数填充到左侧面板"
                      @click="replayOptions(item.draw_id)"
                    ><svg-icon class="icon" icon-class="ic_copy" />{{ '复用参数' | lang }}</span>
                    <span style="font-size: 13px; color: #898989;">{{ item.create_time }}</span>
                  </div>
                </div>
              </div>
              <div class="message row-ai" style="padding: 15px 30px 20px 30px;">
                <div class="avatar">
                  <img src="/static/img/ic_ai.png">
                </div>
                <div class="text markdown-body">
                  <imageComponent
                    :item="item"
                    @retry="retryDraw"
                    @mjCtrl="mjCtrl"
                  />
                </div>
              </div>
              <div style="clear: both" />
            </div>
          </div>
        </el-scrollbar>
      </div>
      <WelcomeDraw v-else />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getUserBalance } from '@/api/user'
import { submitTask, mjCtrl, getHistoryMsg, getDrawResult, getDrawSetting, getDrawCate, getDrawOptions } from '@/api/draw'

import { drawYijian, drawMj, drawOpenai } from '@/views/index/SubSidebar/Draw'
import { ImageComponent, WelcomeDraw } from '@/views/components'

export default {
  name: 'Draw',
  components: { drawYijian, drawMj, drawOpenai, ImageComponent, WelcomeDraw },
  data() {
    return {
      lists: [],
      wordInput: '',
      page: 1,
      pagesize: 10,
      drawSetting: {},
      user_balance_draw: 0,
      drawCate: [],
      cate_id: '',
      is_share: true
    }
  },
  computed: {
    ...mapGetters([
      'user_id',
      'avatar',
      'nickname',
      'page_title',
      'priceSetting'
    ])
  },
  created() {
    this.getDrawSetting()
    this.getHistoryMsg()
    this.getUserBalance()
    this.getDrawCate()
  },
  methods: {
    getDrawSetting() {
      getDrawSetting().then(res => {
        this.drawSetting = res.data
      })
    },
    getUserBalance() {
      getUserBalance().then(res => {
        this.user_balance_draw = res.data.balance_draw
      })
    },
    submitTask() {
      const options = this.getDrawOptions()
      if (!options.words || options.words.length <= 0) {
        this.$message.error('请添加画面描述')
        return
      }
      if (this.drawCate.length > 0 && !this.cate_id) {
        this.$message.error('请选择图片分类')
        return
      }
      this.scrollBottom()

      submitTask({
        platform: this.drawSetting['platform'],
        channel: this.drawSetting['channel'],
        cate_id: this.cate_id,
        is_share: this.is_share ? 1 : 0,
        options: JSON.stringify(options)
      }).then(res => {
        this.lists.push({
          state: this.drawSetting['platform'] === 'mj' ? 0 : 3,
          draw_id: res.data.draw_id,
          message: res.data.message,
          create_time: res.data.create_time,
          platform: this.drawSetting['platform']
        })
        this.clearOptions()
        this.startLoopResult(res.data.draw_id)
        this.getUserBalance()

        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        } else if (res.message.indexOf('充值') !== -1) {
          this.showPay()
          setTimeout(() => {
            this.$message.error(res.message)
          }, 500)
        }
      })
    },

    startLoopResult(draw_id) {
      var si = setInterval(() => {
        getDrawResult({
          draw_id: draw_id
        }).then(res => {
          const state = res.data.state
          if (state === 0) {
            return
          } else if (state === -1) {
            clearInterval(si)
            return
          }
          if (state === 1 || state === 2) {
            this.getUserBalance()
          }

          var lists = this.lists
          lists.forEach((item, index) => {
            if (item.draw_id === draw_id) {
              lists[index].state = state
              if (state === 1) {
                lists[index].response = res.data.images
                clearInterval(si)
              } else if (state === 2) {
                lists[index].errmsg = res.data.message
                clearInterval(si)
              } else if (state === 3) {
                lists[index].response = [res.data.image]
              }
            }
          })
          this.lists = lists
        })
      }, 5000)
    },

    retryDraw(draw_id) {
      submitTask({
        draw_id: draw_id
      }).then(res => {
        var lists = this.lists
        lists.forEach((item, index) => {
          if (item.draw_id === draw_id) {
            lists[index].state = this.drawSetting['platform'] === 'mj' ? 0 : 3
            lists[index].errmsg = ''
            lists[index].response = ''
            this.startLoopResult(draw_id)
          }
        })
        this.lists = lists
        this.getUserBalance()
      })
    },
    mjCtrl(draw_id, type, index) {
      mjCtrl({
        draw_id: draw_id,
        type: type,
        index: index
      }).then(res => {
        this.lists.push({
          state: 0,
          draw_id: res.data.draw_id,
          message: res.data.message,
          platform: this.drawSetting['platform']
        })
        this.startLoopResult(res.data.draw_id)
        this.getUserBalance()
        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      })
    },
    getHistoryMsg() {
      getHistoryMsg({
        platform: this.drawSetting['platform'],
        channel: this.drawSetting['channel'],
        page: this.page,
        pagesize: this.pagesize
      }).then(res => {
        if (res.data.length > 0) {
          var lists = res.data
          this.lists = lists
          if (lists) {
            lists.forEach((item, index) => {
              if (item.state === 0 || item.state === 3) {
                this.startLoopResult(item.draw_id)
              }
            })
          }
        }
        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      })
    },
    getDrawCate() {
      getDrawCate().then(res => {
        this.drawCate = res.data
      })
    },
    getDrawOptions() {
      let options = {}
      if (this.$refs.drawSetting) {
        options = this.$refs.drawSetting.getDrawOptions()
      }
      return options
    },
    clearOptions() {
      if (this.$refs.drawSetting) {
        this.$refs.drawSetting.clearDrawOptions()
      }
    },
    scrollBottom() {
      this.$nextTick(() => {
        if (this.$refs['msglist']) {
          const container = this.$refs['msglist'].wrap
          setTimeout(() => {
            container.scrollTop = container.scrollHeight
          }, 200)
        }
      })
    },
    showLogin() {
      this.$emit('showLogin')
    },
    showPay() {
      if (this.user_id) {
        if (this.priceSetting.draw_vip) {
          this.$emit('showPay', 'vip')
        } else {
          this.$emit('showPay', 'point')
        }
      } else {
        this.$emit('showLogin')
      }
    },
    replayOptions(draw_id) {
      getDrawOptions({
        draw_id: draw_id
      }).then(res => {
        if (res.data.cate_id) {
          this.cate_id = res.data.cate_id
        }
        this.is_share = true
        this.$refs.drawSetting.setDrawOptions(res.data.options)
      })
    }
  }
}
</script>
