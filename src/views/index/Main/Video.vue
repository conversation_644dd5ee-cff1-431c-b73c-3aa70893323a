<template>
  <div class="size-small">
    <div class="platform-list">
      <div v-if="pikaSetting && pikaSetting['is_open']" class="item" :class="{active: activePlatform === 'pika'}" @click="switchPlatform('pika')">Pika</div>
      <div v-if="runwaySetting && runwaySetting['is_open']" class="item" :class="{active: activePlatform === 'runway'}" @click="switchPlatform('runway')">Runway</div>
      <div v-if="stableSetting && stableSetting['is_open']" class="item" :class="{active: activePlatform === 'stable'}" @click="switchPlatform('stable')">Stable Video</div>
      <div v-if="soraSetting && soraSetting['is_open']" class="item" :class="{active: activePlatform === 'sora'}" @click="switchPlatform('sora')">Sora</div>
    </div>
    <div class="sub-sidebar" style="top: 55px;">
      <div class="video-setting">
        <div class="module-body">
          <videoPika
            v-if="activePlatform === 'pika'"
            ref="videoSetting"
            @showLogin="showLogin"
            @showPay="showPay"
          />
          <videoRunway
            v-else-if="activePlatform === 'runway'"
            ref="videoSetting"
            @showLogin="showLogin"
            @showPay="showPay"
          />
          <videoStable
            v-else-if="activePlatform === 'stable'"
            ref="videoSetting"
            @showLogin="showLogin"
            @showPay="showPay"
          />
          <videoSora
            v-else-if="activePlatform === 'sora'"
            ref="videoSetting"
            @showLogin="showLogin"
            @showPay="showPay"
          />
        </div>
        <div v-if="activePlatform === 'pika'" class="module-footer">
          <div v-if="priceSetting.video > 0" class="box-price" @click="showPay">
            <svg-icon class="icon" icon-class="ic_mod_video" />
            <span class="num">{{ priceSetting.video }}</span>{{ priceSetting.title }}/{{ '次' | lang }}
            <span v-if="priceSetting.video_vip" class="vip">会员免费</span>
          </div>
          <div v-else class="box-price" style="cursor: default;">
            <svg-icon class="icon" icon-class="ic_mod_video" />
            <span class="vip">免费使用</span>
          </div>
          <el-button
            class="btn-video"
            type="primary"
            :title="'发送' | lang"
            @click="submitTask"
          >
            <svg-icon icon-class="ic_pika" /> 生成视频
          </el-button>
        </div>
      </div>
    </div>

    <div class="main-wrapper">
      <div v-if="videoShopIsOpen" class="type-list">
        <div class="tab-type">
          <div class="tab-item" :class="{active: listType === 'public'}" @click="switchListType('public')">作品广场</div>
          <div class="tab-item" :class="{active: listType === 'my'}" @click="switchListType('my')">我的作品</div>
        </div>
      </div>
      <div v-if="videoShopIsOpen && listType === 'public'" class="box-msg-list" style="bottom: 0;">
        <el-scrollbar ref="publicList" wrap-class="scrollbar-wrapper">
          <div class="video-list">
            <div v-for="item in publicList" class="video-item" @mouseenter="videoMouseEnter(item.video_id)" @mouseleave="videoMouseLeave(item.video_id)" @click.stop="showVideoDetail(item)">
              <div class="video" :class="{active: hoverVideoId === item.video_id}">
                <video :id="'public_video_' + item.video_id" muted :poster="item.poster" :src="item.video" loop="loop" preload="none" />
                <div class="poster" :style="'background-image: url(' + item.poster + ');'" />
                <div class="btn-play"><div class="icon" style="background-image: url(/static/img/ic_play.png);" /></div>
                <div class="prompt">{{ item.prompt }}</div>
              </div>
            </div>
          </div>
          <div style="width: 100%; margin: 20px 0;">
            <pages :page="publicPage" :page-total="publicPageTotal" @change="changePublicPage" />
          </div>
        </el-scrollbar>
      </div>
      <div v-else-if="listType === 'my' && myList && myList.length > 0" class="box-msg-list style-write" style="bottom: 0;">
        <el-scrollbar ref="myList" wrap-class="scrollbar-wrapper">
          <div class="list" style="width: 862px;" :style="videoShopIsOpen ? 'padding-top: 70px;' : 'padding-top: 0;'">
            <div v-for="item in myList" class="row">
              <div class="message row-user" style="padding: 20px 30px 10px 30px; width: 862px;">
                <div class="avatar" style="background: #9aa37e;">
                  <img :src="avatar || '/static/img/ic_user.png'">
                </div>
                <div class="text markdown-body" style="padding-top: 3px;">
                  {{ item.prompt }}<span v-if="item.action === 'upscale'"> --upscale</span>
                  <div class="tools" style="opacity: 1;">
                    <span
                      class="btn text-primary"
                      title="将参数填充到左侧面板"
                      @click="handleMyVideo('copy', item.video_id)"
                    ><svg-icon class="icon" icon-class="ic_copy" />{{ '复用参数' | lang }}</span>
                    <span style="font-size: 13px; color: #898989;">{{ item.create_time }}</span>
                  </div>
                </div>
              </div>
              <div class="message row-ai" style="padding: 15px 30px 20px 30px; width: 862px;">
                <div class="avatar" style="opacity: 0;" />
                <div class="text" style="width: 720px;">
                  <videoComponent
                    :item="item"
                    @retry="retryVideo"
                  />
                  <div v-if="item.state === 1" style="display: flex; align-items: center; justify-content: space-between; margin: 15px 0 10px 0;">
                    <div class="btn-group">
                      <el-button size="mini" type="default" icon="el-icon-edit-outline" title="编辑本视频的内容" @click="handleMyVideo('edit', item.video_id)">编辑视频</el-button>
                      <el-button size="mini" type="default" icon="el-icon-time" title="将视频延长4秒" @click="handleMyVideo('extend', item.video_id)">延长4s</el-button>
                      <!--<el-button v-if="item.action !== 'upscale'" size="mini" type="default" icon="el-icon-rank" title="无损放大本视频" @click="handleMyVideo('upscale', item.video_id)">无损放大</el-button>-->
                    </div>
                    <span v-if="item.seed" title="种子值 --seed" style="font-size: 13px; color: #898989;"><svg-icon class="icon" icon-class="ic_seed" /> {{ item.seed }}</span>
                  </div>
                </div>
              </div>
              <div style="clear: both" />
            </div>
          </div>
        </el-scrollbar>
      </div>
      <WelcomeVideo v-if="listType === 'public' && publicList.length === 0" desc="广场空空的~" />
      <WelcomeVideo v-if="listType === 'my' && myList.length === 0" desc="您还没有作品，快去创作吧" />
      <videoDetail v-if="activeVideo" :video="activeVideo" @handle="handlePublicVideo" @close="closeVideoDetail" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { setStorage, getStorage } from '@/utils/auth'
import { getUserBalance } from '@/api/user'
import { submitTask, getHistoryMsg, getPublicList, getVideoResult, getVideoSetting, getVideoOptions, upscaleVideo } from '@/api/video'

import { videoPika, videoRunway, videoStable, videoSora } from '@/views/index/SubSidebar/Video'
import { VideoComponent, WelcomeVideo, videoDetail } from '@/views/components'
import pages from '@/views/components/Pages'

import fileSaver from 'file-saver'

export default {
  name: 'Video',
  components: { videoPika, videoRunway, videoStable, videoSora, VideoComponent, WelcomeVideo, videoDetail, pages },
  data() {
    return {
      activePlatform: 'pika',
      listType: 'public',
      publicList: [],
      publicPage: 1,
      publicPageSize: 30,
      publicTotalCount: 0,
      myList: [],
      page: 1,
      pagesize: 10,
      activeVideo: null,
      hoverVideoId: null,
      pikaSetting: {},
      runwaySetting: {},
      stableSetting: {},
      soraSetting: {},
      user_balance_video: 0,
      is_share: true
    }
  },
  computed: {
    ...mapGetters([
      'user_id',
      'avatar',
      'nickname',
      'priceSetting'
    ]),
    publicPageTotal() {
      return Math.ceil(this.publicTotalCount / this.publicPageSize)
    },
    videoShopIsOpen() {
      if (this.activePlatform === 'pika') {
        return !!this.pikaSetting['shop_open']
      } else if (this.activePlatform === 'runway') {
        return !!this.runwaySetting['shop_open']
      } else if (this.activePlatform === 'stable') {
        return !!this.stableSetting['shop_open']
      } else if (this.activePlatform === 'sora') {
        return !!this.soraSetting['shop_open']
      }
      return false
    }
  },
  created() {
    const videoListType = getStorage('videoListType')
    if (videoListType) {
      this.listType = videoListType
    }
    this.getVideoSetting()
    this.getUserBalance()
  },
  methods: {
    switchPlatform(platform) {
      this.activePlatform = platform
      this.publicPage = 1
      this.publicTotalCount = 0
      this.publicList = []
      if (this.videoShopIsOpen) {
        this.getPublicList()
      } else {
        this.switchListType('my')
      }
      this.getHistoryMsg()
    },
    switchListType(type) {
      this.listType = type
      setStorage('videoListType', type)
    },
    getVideoSetting() {
      getVideoSetting().then(res => {
        this.pikaSetting = res.data.pika
        this.runwaySetting = res.data.runway
        this.stableSetting = res.data.stable
        this.soraSetting = res.data.sora
        if (this.pikaSetting['is_open']) {
          this.switchPlatform('pika')
        } else if (this.runwaySetting['is_open']) {
          this.switchPlatform('runway')
        } else if (this.stableSetting['is_open']) {
          this.switchPlatform('stable')
        } else if (this.soraSetting['is_open']) {
          this.switchPlatform('sora')
        }
      }).catch(err => {
        if (err.message.indexOf('已停用') !== -1) {
          setTimeout(() => {
            this.$router.push({
              name: 'chat'
            })
          }, 500)
        }
      })
    },
    getUserBalance() {
      getUserBalance().then(res => {
        this.user_balance_video = res.data.balance_video
      })
    },
    submitTask() {
      const options = this.getVideoOptions()
      if (!options.prompt) {
        this.$message.error('请输入视频描述')
        return
      }

      submitTask({
        platform: this.activePlatform,
        options: JSON.stringify(options)
      }).then(res => {
        this.switchListType('my')
        this.myList.push({
          state: 0,
          video_id: res.data.video_id,
          action: options.action,
          prompt: res.data.prompt,
          seed: res.data.seed ? res.data.seed : '',
          create_time: res.data.create_time,
          platform: this.activePlatform
        })
        this.clearOptions()
        setTimeout(() => {
          this.startLoopResult(res.data.video_id)
        }, 20000)
        this.getUserBalance()

        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        } else if (res.message.indexOf('充值') !== -1) {
          this.showPay()
          setTimeout(() => {
            this.$message.error(res.message)
          }, 500)
        }
      })
    },

    startLoopResult(video_id) {
      setTimeout(() => {
        getVideoResult({
          video_id: video_id
        }).then(res => {
          const state = res.data.state
          if (state === -1) {
            return
          }
          if (state === 0) {
            this.startLoopResult(video_id)
            return
          }
          if (state === 1 || state === 2) {
            var lists = this.myList
            lists.forEach((item, index) => {
              if (item.video_id === video_id) {
                lists[index].state = state
                if (state === 1) {
                  lists[index].video = res.data.video
                  lists[index].poster = res.data.poster
                  lists[index].seed = res.data.seed
                } else if (state === 2) {
                  lists[index].errmsg = res.data.message
                }
              }
            })
            this.myList = lists
            setTimeout(() => {
              this.getUserBalance()
            }, 300)
          }
        }).catch(() => {
          this.startLoopResult(video_id)
        })
      }, 10000)
    },

    retryVideo(video_id) {
      submitTask({
        video_id: video_id
      }).then(res => {
        var lists = this.myList
        lists.forEach((item, index) => {
          if (item.video_id === video_id) {
            lists[index].state = 0
            lists[index].errmsg = ''
            lists[index].video = ''
            setTimeout(() => {
              this.startLoopResult(video_id)
            }, 20000)
          }
        })
        this.myList = lists
        this.getUserBalance()
      })
    },
    getHistoryMsg() {
      getHistoryMsg({
        platform: this.activePlatform,
        page: this.page,
        pagesize: this.pagesize
      }).then(res => {
        if (res.data.length > 0) {
          var lists = res.data
          if (lists) {
            lists.forEach((item, index) => {
              if (item.state === 0 || item.state === 3) {
                this.startLoopResult(item.video_id)
              }
            })
          }
          this.myList = lists
        }
        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      })
    },
    getPublicList() {
      getPublicList({
        page: this.publicPage,
        pagesize: this.publicPageSize,
        platform: this.activePlatform
      }).then(res => {
        this.publicList = res.data.list
        this.publicTotalCount = res.data.count

        this.$nextTick(() => {
          if (this.$refs['publicList']) {
            const container = this.$refs['publicList'].wrap
            setTimeout(() => {
              container.scrollTop = 0
            }, 200)
          }
        })
      })
    },
    changePublicPage(page) {
      this.publicPage = page
      this.getPublicList()
    },
    getVideoOptions() {
      let options = {}
      if (this.$refs.videoSetting) {
        options = this.$refs.videoSetting.getVideoOptions()
      }
      return options
    },
    clearOptions() {
      if (this.$refs.videoSetting) {
        this.$refs.videoSetting.clearVideoOptions()
      }
    },
    scrollBottom() {
      this.$nextTick(() => {
        if (this.$refs['myList']) {
          const container = this.$refs['myList'].wrap
          setTimeout(() => {
            container.scrollTop = container.scrollHeight
          }, 200)
        }
      })
    },
    showLogin() {
      this.$emit('showLogin')
    },
    showPay() {
      if (this.user_id) {
        if (this.priceSetting.video_vip) {
          this.$emit('showPay', 'vip')
        } else {
          this.$emit('showPay', 'point')
        }
      } else {
        this.$emit('showLogin')
      }
    },
    download(video) {
      fileSaver.saveAs(video, 'video.mp4')
    },
    showVideoDetail(video) {
      this.activeVideo = video
    },
    closeVideoDetail() {
      this.activeVideo = null
    },
    setVideoOptions(options) {
      this.$refs.videoSetting.setVideoOptions(options)
    },
    handleMyVideo(action, video_id) {
      this.closeVideoDetail()
      getVideoOptions({
        type: 'my',
        video_id: video_id
      }).then(res => {
        var options = res.data.options
        if (action === 'upscale') {
          this.upscaleVideo('my', video_id)
          return
        }
        if (action === 'copy') {
          options.action = 'generate'
        } else if (action === 'edit') {
          options.action = 'edit'
          options.video = res.data.video_url_ori
          options.image = ''
        } else if (action === 'extend') {
          options.action = 'extend'
          options.video = res.data.video_url_ori
          options.image = ''
        }
        this.setVideoOptions(options)
      }).catch(err => {
        if (err.message.indexOf('请登录') !== -1) {
          this.showLogin()
        }
      })
    },
    handlePublicVideo(action, video_id) {
      this.closeVideoDetail()
      getVideoOptions({
        type: 'public',
        video_id: video_id
      }).then(res => {
        var options = res.data.options
        if (action === 'upscale') {
          this.upscaleVideo('public', video_id)
          return
        }
        if (action === 'copy') {
          options.action = 'generate'
        } else if (action === 'edit') {
          options.action = 'edit'
          options.video = res.data.video_url_ori
          options.image = ''
        } else if (action === 'extend') {
          options.action = 'extend'
          options.video = res.data.video_url_ori
          options.image = ''
        }
        this.setVideoOptions(options)
      }).catch(err => {
        if (err.message.indexOf('请登录') !== -1) {
          this.showLogin()
        }
      })
    },
    upscaleVideo(type, video_id) {
      upscaleVideo({
        type: type,
        video_id: video_id
      }).then(res => {
        this.switchListType('my')
        this.myList.push({
          state: 0,
          video_id: res.data.video_id,
          action: 'upscale',
          prompt: res.data.prompt,
          seed: res.data.seed ? res.data.seed : '',
          create_time: res.data.create_time,
          platform: this.activePlatform
        })
        this.clearOptions()
        setTimeout(() => {
          this.startLoopResult(res.data.video_id)
        }, 20000)
        this.getUserBalance()

        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        } else if (res.message.indexOf('充值') !== -1) {
          this.showPay()
          setTimeout(() => {
            this.$message.error(res.message)
          }, 500)
        }
      })
    },
    videoMouseEnter(video_id) {
      if (this.hoverVideoId) {
        this.videoMouseOut(this.hoverVideoId)
      }
      this.hoverVideoId = video_id
      document.getElementById('public_video_' + video_id).play()
    },
    videoMouseLeave(video_id) {
      if (this.hoverVideoId === video_id) {
        document.getElementById('public_video_' + video_id).currentTime = 0
        document.getElementById('public_video_' + video_id).pause()
        this.hoverVideoId = null
      }
    }
  }
}
</script>
