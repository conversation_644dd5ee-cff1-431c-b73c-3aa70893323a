<template>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <div class="box-draw-setting">
        <div class="setting-row">
          <div class="setting-item" style="padding-bottom: 12px;">
            <div class="header">✳ {{ '选择类型' | lang }}</div>
            <div class="options">
              <div v-for="item in settings.styleDetails" class="option-item" :class="{active: group_name === item.GroupName}" @click="switchGroup(item.GroupName)" style="width: 132px; height: 80px;">
                <img :src="item.ShowImage" :alt="item.GroupName">
                <span>{{ item.GroupName }}</span>
              </div>
              <div style="clear:both;"></div>
            </div>
          </div>
        </div>
        <div class="setting-row" v-if="styles && styles.length > 0">
          <div class="setting-item" style="padding-bottom: 12px;">
            <div class="header">✳ {{ '选择风格' | lang }}</div>
            <div class="options">
              <div v-for="item in styles" class="option-item" :class="{active: engine === item.engine && style === item.value}" @click="switchEngine(item.engine, item.value)" :title="item.text">
                <img :src="item.poster" :alt="item.text">
                <span>{{ item.text }}</span>
              </div>
              <div style="clear:both;"></div>
            </div>
          </div>
        </div>
        <div class="setting-row" v-if="sub_styles && sub_styles.length > 0">
          <div class="setting-item" style="padding-bottom: 12px;">
            <div class="header">✳ {{ '选择模式' | lang }}</div>
            <div class="options">
              <div v-for="item in sub_styles" class="option-item" :class="{active: sub_style === item.value}" @click="switchSubStyle(item.value)" :title="item.text">
                <img :src="item.poster" :alt="item.text">
                <span>{{ item.text }}</span>
              </div>
              <div style="clear:both;"></div>
            </div>
          </div>
        </div>
        <div class="setting-row">
          <prompt ref="prompt" type="yijian" style="width: 100%;"></prompt>
        </div>
        <div class="setting-row">
          <div class="setting-item col-1">
            <div class="header">
              <span>✳ {{ '参考图片' | lang }}</span>
              <div style="display: flex; align-items: center;">
                <el-tooltip effect="dark" content="设置参考图，尺寸限1000x1000像素以内" placement="bottom">
                  <i class="el-icon-warning-outline tips" />
                </el-tooltip>
              </div>
            </div>
            <div class="options">
              <el-upload
                class="avatar-uploader"
                action=""
                :http-request="uploadImage"
                :show-file-list="false"
                :multiple="false"
                title="点击上传图片"
              >
                <div v-if="image" class="image">
                  <img :src="image" />
                  <span class="del" title="移除图片" @click.stop="removeImage">
                  <i class="el-icon-close"></i>
                </span>
                </div>
                <i v-else class="el-icon-plus avatar-uploader-icon" style="width: 92px; height: 92px; line-height:92px;" />
              </el-upload>
              <div style="clear:both;" />
            </div>
          </div>
          <div class="setting-item col-3">
            <div class="header">
              <span>✳ {{ '画布比例' | lang }}</span>
              <el-tooltip effect="dark" content="指定生成图像的宽高比" placement="bottom">
                <i class="el-icon-warning-outline tips" />
              </el-tooltip>
            </div>
            <div class="options" style="display: flex; justify-content: space-around;">
              <div v-for="item in settings.imageSizes" class="size-item" :class="{active: size === item.value}" @click="switchSize(item.value)" :title="item.desc">
                <div :class="'size-block size' + item.value"></div>
                <span>{{ item.text }}</span>
              </div>
              <div style="clear:both;" />
            </div>
          </div>
        </div>

      </div>
    </el-scrollbar>
</template>

<script>
import prompt from './prompt'
import { uploadImage } from '@/api/upload';

export default {
  components: { prompt },
  props: {
    settings: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      group_name: '',
      style: '',
      engine: '',
      sub_style: '',
      styles: [],
      image: '',
      size: 0
    }
  },
  watch: {
    group_name() {
      this.settings.styleDetails.forEach(item => {
        if(this.group_name === item.GroupName) {
          this.styles =  item.Styles
          // 默认选第一个
          if (item.Styles.length > 0) {
            this.engine = item.Styles[0].engine
            this.style = item.Styles[0].value
          } else {
            this.engine = ''
            this.style = ''
          }
        }
      })
    }
  },
  computed: {
    sub_styles() {
      var sub_styles = []
      this.styles.forEach(item => {
        if (this.engine === item.engine && this.style === item.value) {
          // 默认选第一个
          if (item.sub_styles.length > 0) {
            this.sub_style = item.sub_styles[0].value
          } else {
            this.sub_style = ''
          }
          sub_styles= item.sub_styles
        }
      })
      return sub_styles
    }
  },
  created() {
    var groups = []
    this.settings.styleDetails.forEach((item, index) => {
      groups.push({
        title: item.GroupName,
        poster: item.ShowImage
      })
      if(index === 0) {
        this.group_name = item.GroupName
      }
    })
    this.groups = groups
  },
  methods: {
    switchGroup(group_name) {
      this.group_name = group_name
    },
    switchEngine(engine, style) {
      this.engine = engine
      this.style = style
    },
    switchSubStyle(sub_style) {
      this.sub_style = sub_style
    },
    switchSize(size) {
      this.size = size
    },
    getDrawOptions() {
      let options = {
        engine: this.engine,
        style: this.style,
        sub_style: this.sub_style,
        size: this.size,
        image: this.image
      }
      options.words = this.$refs['prompt'].getWordsPicked()
      return options
    },
    clearDrawOptions() {
      this.$refs.prompt.clearPicked()
      this.image = ''
    },
    setDrawOptions(options) {
      this.engine = options.engine ? options.engine : 'default_dreamer_diffusion'
      this.style = options.style ? options.style : ''
      this.sub_style = options.sub_style ? options.sub_style : ''
      this.size = options.size ? options.size :0
      this.image = options.image ? options.image : ''
      if (options.words) {
        this.$refs.prompt.setWords(options.words)
      }
    },
    uploadImage(item) {
      var form = new FormData()
      form.append('image', item.file)
      if (item.data) {
        form.append('data', JSON.stringify(item.data))
      }
      uploadImage(form).then(res => {
        this.image = res.data.path
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    removeImage() {
      this.image = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.draw-setting .module-body .box-draw-setting .setting-row {
  .image {
    display: block;
    height: 92px;
    width: auto;
    min-width: 92px;
    max-width: 180px;
    position: relative;
    text-align: center;

    img {
      height: 92px;
      width: auto;
    }
  }
}
</style>
