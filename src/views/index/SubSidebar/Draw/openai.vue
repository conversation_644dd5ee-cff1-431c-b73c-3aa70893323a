<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="box-draw-setting">
      <div class="setting-row">
        <div class="setting-item" style="width: 320px;">
          <div class="header">
            <span>✳ {{ '尺寸' | lang }}</span>
            <el-tooltip effect="dark" content="指定生成图像的宽高比" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-radio-group v-model="size" size="small">
              <el-radio-button label="1024x1024">1024x1024</el-radio-button>
              <el-radio-button label="1024x1792">1024x1792</el-radio-button>
              <el-radio-button label="1792x1024">1792x1024</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="setting-item" style="width: 180px;">
          <div class="header">
            <span>✳ {{ '质量' | lang }}</span>
          </div>
          <div class="options">
            <el-radio-group v-model="quality" size="small">
              <el-radio-button label="standard">标准</el-radio-button>
              <el-radio-button label="hd">高清</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="setting-item" style="width: 180px;">
          <div class="header">
            <span>✳ {{ '风格' | lang }}</span>
          </div>
          <div class="options">
            <el-radio-group v-model="style" size="small">
              <el-radio-button label="natural">自然</el-radio-button>
              <el-radio-button label="vivid">鲜艳</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="setting-row">
        <prompt ref="prompt" type="openai" style="width: 100%;"></prompt>
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
import prompt from './prompt'
export default {
  components: { prompt },
  data() {
    return {
      size: '1024x1024',
      quality: 'standard',
      style: 'natural'
    }
  },
  methods: {
    switchSize(size) {
      this.size = size
    },
    getDrawOptions() {
      let options = {
        size: this.size,
        quality: this.quality,
        style: this.style
      }
      options.words = this.$refs['prompt'].getWordsPicked()
      return options
    },
    clearDrawOptions() {
      this.$refs.prompt.clearPicked()
    },
    setDrawOptions(options) {
      this.size = options.size ? options.size : '1024x1024'
      this.quality = options.quality ? options.quality : 'standard'
      this.style = options.style ? options.style : 'natural'
      if (options.words) {
        this.$refs.prompt.setWords(options.words)
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.size-list {
  width: 210px;
  .item {
    width: 60px;
    height: 40px;
    border: 1px solid #ddd;
    float: left;
    margin-right: 10px;
  }
}
</style>
