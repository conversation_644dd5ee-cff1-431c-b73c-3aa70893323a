<template>
  <div>
    <div class="module-header">
      <div class="search">
        <el-input v-model="searchKeyword" :placeholder="'搜索模型' | lang" prefix-icon="el-icon-search" size="large" :clearable="true" autocomplete />
      </div>
    </div>
    <div class="module-body">
      <div class="group-write fixed-topic">
        <div v-if="activeTopic" class="topic-title" :class="{is_collapse: activeTopic.collapse}" @click="doCollapse(activeTopic.id)">
          <span>{{ activeTopic.title }}</span>
          <i><svg-icon icon-class="ic_collapse" class="collapse" /></i>
        </div>
      </div>
      <el-scrollbar ref="scroll" wrap-class="scrollbar-wrapper">
        <div v-for="(topic, idx) in topicList" class="group-write">
          <div v-if="topic.prompts && topic.prompts.length > 0 && !topic.hidden" :ref="'topic' + topic.id" :data-id="'topic' + topic.id" class="topic-title" :class="{is_collapse: topic.collapse}" @click="doCollapse(topic.id)">
            <span>{{ topic.title }}</span>
            <i><svg-icon icon-class="ic_collapse" class="collapse" /></i>
          </div>
          <div v-if="topic.prompts && topic.prompts.length > 0 && !topic.hidden" class="prompt-list" :style="'height: ' + (topic.collapse ? 0 : topic.listHeight) + 'px;'">
            <div v-for="(prompt,idx2) in topic.prompts" class="prompt-item" :class="{active: activePrompt && activePrompt.id === prompt.id, hidden: prompt.hidden}" @click="changePrompt(prompt)">
              <div class="prompt-title">{{ prompt.title }}</div>
              <div class="prompt-desc">{{ prompt.desc }}</div>
              <div class="prompt-ops">
                <div class="op-item" :title="'点击量' | lang">
                  <svg-icon class="icon" icon-class="ic_view" />
                  {{ prompt.views }}
                </div>
                <div class="op-item" :title="'使用量' | lang">
                  <svg-icon class="icon" icon-class="ic_usage" />
                  {{ prompt.usages }}
                </div>
                <div class="op-item" :title="'收藏' | lang" @click.stop="doPromptVote(idx, idx2)">
                  <svg-icon v-if="prompt.isVote === 1" class="icon" icon-class="ic_vote_active" />
                  <svg-icon v-else class="icon" icon-class="ic_vote" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { getAllPrompt, votePrompt } from '@/api/write'
import { promptAll } from '@/views/components'

export default {
  components: { promptAll },
  data() {
    return {
      topicList: [],
      activeTopic: null,
      activePrompt: null,
      searchKeyword: ''
    }
  },
  watch: {
    searchKeyword() {
      this.filterList()
    }
  },
  mounted() {
    this.getAllPrompt()
    this.$nextTick(() => {
      setTimeout(() => {
        this.onScroll()
      }, 300)
    })
  },
  methods: {
    filterList() {
      const keyword = this.searchKeyword.toLowerCase()
      const topicList = this.topicList
      topicList.forEach((topic, idx1) => {
        if (topic && topic['prompts'] && topic['prompts'].length > 0) {
          let topicHidden = true
          let count = 0
          topic['prompts'].forEach((prompt, idx2) => {
            if (prompt.title.toLowerCase().indexOf(keyword) === -1) {
              topic['prompts'][idx2].hidden = true
            } else {
              count++
              topic['prompts'][idx2].hidden = false
              topicHidden = false
            }
          })
          topic.hidden = topicHidden
          topic.listHeight = count * 120
          topicList[idx1] = topic
        }
      })
      this.topicList = topicList
      this.$forceUpdate()
    },
    onScroll() {
      const scrollEl = this.$refs.scroll.wrap
      scrollEl.onscroll = () => {
        this.handleScroll()
      }
    },
    handleScroll() {
      const scrollEl = this.$refs.scroll.wrap
      const topicList = this.topicList
      for (var i = topicList.length - 1; i >= 0; i--) {
        if (topicList[i].hidden || topicList[i].prompts.length === 0) {
          continue
        }
        if (this.$refs['topic' + topicList[i].id][0].offsetTop <= scrollEl.scrollTop) {
          this.activeTopic = topicList[i]
          return
        }
      }
    },
    getAllPrompt() {
      getAllPrompt().then(res => {
        const topicList = res.data
        const activePromptId = parseInt(this.$route.query.id)
        let scrollTop = 0
        topicList.forEach((topic, index) => {
          topicList[index].listHeight = topic['prompts'].length * 120
          if (activePromptId) {
            topic['prompts'].forEach(prompt => {
              scrollTop += 122
              if (prompt.id === activePromptId) {
                this.activePrompt = prompt
                this.scrollTop = scrollTop - 400
                this.$emit('changePrompt', prompt.id)
              }
            })
          }
        })
        this.topicList = topicList
        this.$nextTick(() => {
          if (this.activePrompt && this.scrollTop) {
            this.$refs.scroll.wrap.scrollTop = this.scrollTop
            this.handleScroll()
          }
        })

        // 筛选出收藏的prompt
        // this.topicList.forEach(topic => {
        //   if (topic.prompts && topic.prompts.length > 0) {
        //     topic.prompts.forEach(prompt => {
        //       if (prompt.isVote) {
        //         voteList.push(prompt)
        //       }
        //     })
        //   }
        // })
        // this.voteList = voteList
      })
    },
    doPromptVote(idx, idx2) {
      const prompt = this.topicList[idx].prompts[idx2]

      votePrompt({
        prompt_id: prompt.id
      }).then(res => {
        this.$message.success(res.message)
        this.topicList[idx].prompts[idx2].isVote = prompt.isVote ? 0 : 1
        this.$forceUpdate()
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    changePrompt(prompt) {
      this.activePrompt = prompt
      this.$emit('switchModule', 'write', prompt.id)
    },
    getActiveId() {
      return this.activePrompt ? this.activePrompt.id : 0
    },
    doCollapse(id) {
      const topicList = this.topicList
      let thisTopic = null
      const scrollEl = this.$refs.scroll.wrap

      // 折叠
      topicList.forEach((topic, index) => {
        if (topic.id === id) {
          topicList[index].collapse = !topic.collapse
          thisTopic = this.$refs['topic' + topic.id][0]
        }
      })
      this.topicList = topicList
      this.$forceUpdate()

      this.$nextTick(() => {
        // 点击吸顶分类, 滚动到合适的位置
        if (this.activeTopic && id === this.activeTopic.id) {
          setTimeout(() => {
            scrollEl.scrollTop = thisTopic.offsetTop
          }, 500)
        }
      })
    }
  }
}
</script>
