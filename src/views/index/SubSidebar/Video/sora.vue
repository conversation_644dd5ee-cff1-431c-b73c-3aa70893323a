<template>
  <div class="closed">
    <p><svg-icon class="icon" icon-class="ic_working" /></p>
    <p>Sora 内测中，静待开放</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      defaultOptions: {},
      options: {}
    }
  },
  created() {

  },
  methods: {

  }
}
</script>
<style lang="scss" scoped>
.closed {
  opacity: 0.5;
  transition: all 0.5s linear;
  width: 260px;
  margin: 100px auto 0 auto;
  padding: 10px;

  p {
    text-align: center;
    margin: 30px 0;
    padding: 0;
    color: #999;
    font-size: 14px;
    .icon {
      font-size: 120px;
      color: #d6dcdc;
    }
  }
  &:hover {
    opacity: 1;
  }
}
</style>
