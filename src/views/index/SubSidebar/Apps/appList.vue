<template>
  <div class="apps">
    <div class="header">应用中心</div>
    <div class="module-body">
      <el-scrollbar ref="scroll" wrap-class="scrollbar-wrapper">
        <div>
          <div class="app-list">
            <div v-if="pkIsOpen" class="app-item" :class="{active: activeApp && activeApp === 'pk'}" @click="changeApp('pk')">
              <div class="thumb">
                <svg-icon class="icon" icon-class="ic_app_pk" style="font-size: 36px;" />
              </div>
              <div style="width: 134px;">
                <div class="title">{{ 'AI擂台' | lang }}</div>
                <div class="desc">孰强孰弱，Battle一下</div>
              </div>
            </div>
            <div v-if="batchIsOpen" class="app-item" :class="{active: activeApp && activeApp === 'batch'}" @click="changeApp('batch')">
              <div class="thumb">
                <svg-icon class="icon" icon-class="ic_app_batch" style="font-size: 36px;" />
              </div>
              <div style="width: 134px;">
                <div class="title">{{ '批量生成' | lang }}</div>
                <div class="desc">无人值守批量创作</div>
              </div>
            </div>
            <div v-if="novelIsOpen" class="app-item" :class="{active: activeApp && activeApp === 'novel'}" @click="changeApp('novel')">
              <div class="thumb">
                <svg-icon class="icon" icon-class="ic_app_novel" style="font-size: 36px;" />
              </div>
              <div style="width: 134px;">
                <div class="title">{{ '长篇写作' | lang }}</div>
                <div class="desc">写小说、写论文等</div>
              </div>
            </div>
            <div v-if="mindIsOpen" class="app-item" :class="{active: activeApp && activeApp === 'mind'}" @click="changeApp('mind')">
              <div class="thumb">
                <svg-icon class="icon" icon-class="ic_app_mind" style="font-size: 36px;" />
              </div>
              <div style="width: 134px;">
                <div class="title">{{ '思维导图' | lang }}</div>
                <div class="desc">生成思维导图</div>
              </div>
            </div>
<!--            <div class="app-item" :class="{active: activeApp && activeApp === 'team'}" @click="changeApp('team')">
              <div class="thumb">
                <svg-icon class="icon" icon-class="ic_app_team" style="font-size: 36px;" />
              </div>
              <div style="width: 134px;">
                <div class="title">{{ 'AI团队' | lang }}</div>
                <div class="desc">分工合作，生产力暴增</div>
              </div>
            </div>-->
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      activeApp: null
    }
  },
  computed: {
    ...mapGetters([
      'pkIsOpen',
      'batchIsOpen',
      'novelIsOpen',
      'teamIsOpen',
      'mindIsOpen'
    ])
  },
  mounted() {
    if (this.$route.name === 'apps') {
      this.$emit('switchModule', 'apps')
    } else {
      this.activeApp = this.$route.name
    }
  },
  methods: {
    changeApp(name) {
      this.activeApp = name
      this.$router.push({
        name: name
      }).catch(err => {})
      this.$emit('switchModule', 'apps', name)
    }
  }
}
</script>
