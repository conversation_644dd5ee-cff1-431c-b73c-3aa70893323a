<template>
  <div class="box-task">
    <div class="task-toolbar">
      <div style="width: 550px; display: flex; justify-content: space-between; align-items: center;">
        <div>
          <el-button size="small" icon="el-icon-arrow-left" type="primary" @click="toBack">返回</el-button>
          <el-button size="small" icon="el-icon-plus" type="default" @click="showTaskForm(null)">添加任务</el-button>
          <el-button size="small" icon="el-icon-upload2" type="default" @click="showImportForm">批量导入任务</el-button>
        </div>
        <div>
          <el-button v-if="this.taskList && this.taskList.length > 0" size="small" type="default" :loading="taskRunning" @click="startAllTask">
            <svg-icon v-if="!taskRunning" icon-class="ic_play" /> 全部开始
          </el-button>
        </div>
      </div>
      <div>
        <el-button size="small" icon="el-icon-s-tools" type="default" @click="openSetting">设置</el-button>
        <el-button size="small" icon="el-icon-brush" type="default" @click="delAllTask">清空任务列表</el-button>
        <el-button size="small" icon="el-icon-download" type="default" @click="exportZip">打包下载</el-button>
      </div>
    </div>
    <div v-if="activeIndex >= 0" class="task-main">
      <el-scrollbar ref="task-list" wrap-class="scrollbar-wrapper" class="task-list">
        <div v-for="(item, index) in taskList" class="task-item" :class="{ active: taskList[activeIndex].id === item.id }" @click="switchTask(index)">
          <div class="td index">
            <span># {{ tableIndex(index) }}</span>
          </div>
          <div class="td state">
            <el-tag v-if="item.state === 1" type="success" size="small">已完成</el-tag>
            <el-tag v-else-if="index === activeIndex && writing" type="warning" size="small">生成中</el-tag>
            <el-tag v-else type="info" size="small">未开始</el-tag>
          </div>
          <div class="td title">
            {{ item.message }}
          </div>
          <div class="ops">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                <i class="btn-dropdown el-icon-more" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native.stop="showTaskForm(index)">{{ '编辑' | lang }}</el-dropdown-item>
                <el-dropdown-item class="text-danger" @click.native.stop="delTask(index)">{{ '删除' | lang }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-scrollbar>
      <div class="task-chat box-msg-list style-word">
        <el-scrollbar ref="chat-list" wrap-class="scrollbar-wrapper">
          <div v-if="activeIndex >= 0" class="list" style="padding-top: 30px;">
            <div class="row row-user">
              <div class="message">
                <div class="text markdown-body" v-html="textFormat(taskList[activeIndex].message)" />
              </div>
              <div style="clear: both" />
            </div>
            <div v-if="taskList[activeIndex].response" class="row row-ai">
              <div class="message">
                <div class="text markdown-body">
                  <textComponent
                    :id="'message-' + taskList[activeIndex].id"
                    :text="taskList[activeIndex].response"
                  />
                  <div class="tools">
                    <span class="btn text-primary" @click="copyText"><svg-icon class="icon" icon-class="ic_copy" />{{ '复制内容' | lang }}</span>
                    <span class="btn text-primary" :title="'导出Word' | lang" @click="exportDocx"><svg-icon class="icon" icon-class="ic_word" />{{ '导出Word' | lang }}</span>
                    <div style="float: right;">
                      <span class="btn text-primary" style="cursor: default;">字数 {{ taskList[activeIndex].response.length }}</span>
                      <span class="btn text-primary" :title="'重新生成' | lang" style="margin-right: 0;" @click="retry"><svg-icon class="icon" icon-class="ic_retry" />{{ '重新生成' | lang }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div style="clear: both" />
            </div>
            <div v-else-if="writing || taskList[activeIndex].writingText" class="row row-ai">
              <div class="message">
                <div class="text markdown-body">
                  <textComponent
                    :text="writingText"
                    :writing="!!(writing || writingText)"
                  />
                  <div class="tools">
                    <span
                      class="btn text-primary"
                      @click="stopFetch"
                    ><svg-icon class="icon" icon-class="ic_stop" />{{ '停止生成' | lang }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="row row-ai">
              <div class="message">
                <div class="text markdown-body">
                  <span
                    class="btn text-primary"
                    style="cursor: pointer;"
                    @click="sendText"
                  ><svg-icon class="icon" icon-class="ic_play" />{{ '开始生成' | lang }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div v-else class="task-empty">
      <div class="box-empty">
        <p class="icon"><svg-icon icon-class="ic_empty" /></p>
        <p class="tips">请先添加或导入任务</p>
      </div>
    </div>

    <div v-if="taskForm">
      <el-dialog
        custom-class="my-dialog"
        :title="taskForm.id ? '编辑' : '添加任务' | lang"
        width="600px"
        :visible="true"
        :close-on-click-modal="true"
        :append-to-body="true"
        :before-close="closeTaskForm"
      >
        <el-form ref="form" :model="taskForm" label-width="90px" style="padding: 20px 0;">
          <el-form-item :label="'内容' | lang">
            <el-input v-model="taskForm.message" type="textarea" :rows="6" placeholder="请输入" size="normal" style="width: 430px;" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="default" icon="el-icon-close" size="small" @click="closeTaskForm">{{ '取 消' | lang }}</el-button>
          <el-button type="primary" icon="el-icon-check" size="small" @click="saveTask">{{ '提 交' | lang }}</el-button>
        </span>
      </el-dialog>
    </div>

    <div v-if="importForm">
      <el-dialog
        custom-class="my-dialog"
        :title="'批量导入任务' | lang"
        width="400px"
        :visible="true"
        :close-on-click-modal="true"
        :append-to-body="true"
        :before-close="closeImportForm"
      >
        <div style="text-align: center">
          <p>
            <el-upload
              :before-upload="checkExcelFile"
              :on-success="importTaskSuccess"
              :show-file-list="false"
              action="/web.php/batch/importTask"
              :data="{ batch_id: this.batch_id }"
              class="btn-import"
            >
              <el-button type="primary" icon="el-icon-upload2" size="large">{{ '选择 Excel 文件' | lang }}</el-button>
            </el-upload>
          </p>
          <p style="font-size: 14px; margin: 30px 0;">
            <a href="/static/task.xlsx" class="text-primary">
              <span style="margin-left: 10px;">下载导入模板</span>
            </a>
          </p>
        </div>

      </el-dialog>
    </div>
    <div v-if="zipExporting" style="opacity: 0;">
      <div v-for="(item, index) in taskList">
        <textComponent
          v-if="item.state === 1 && item.response"
          :id="'zipHtml-' + item.id"
          :text="item.response"
        />
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getTaskList, delTask, saveTask, delAllTask, getBatchInfo } from '@/api/batch'
import { getSiteCode, getToken } from '@/utils/auth'
import jszip from 'jszip'
import htmlDocx from 'html-docx-js/dist/html-docx'
import fileSaver from 'file-saver'
import { TextComponent } from '@/views/components'

var textStacks = []
var textOutputSi = 0
var fetchCtrl = null
export default {
  name: 'Batch',
  components: { TextComponent },
  props: {
    batch_id: {
      type: String,
      default: ''
    },
    ai: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeIndex: -1,
      taskList: [],
      taskForm: null,
      taskRunning: false, // 是否正处于自动执行中
      importForm: null,
      writing: false,
      writingText: '',
      zipExporting: false
    }
  },
  computed: {
    ...mapGetters([
      'priceSetting'
    ])
  },
  mounted() {
    if (!this.batch_id) {
      this.toBack()
      return
    }
    this.getTaskList()
  },
  methods: {
    async sendText() {
      if (this.writing || this.writingText) {
        return
      }
      const message = this.trim(this.taskList[this.activeIndex].message)
      if (!message) {
        this.$message.error('无内容')
        return
      }

      if (textOutputSi) {
        clearInterval(textOutputSi)
        textOutputSi = 0
        textStacks = []
      }

      this.writing = true
      this.scrollBottom()

      const headers = new Headers()
      headers.append('Content-Type', 'application/json')
      headers.append('X-token', getToken() || '')
      headers.append('X-site', getSiteCode() || '')
      const url = process.env.VUE_APP_BASE_API + '/chat/sendText'
      const data = {
        message: message,
        batch_id: this.batch_id,
        task_id: this.taskList[this.activeIndex].id
      }
      fetchCtrl = new AbortController()
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        signal: fetchCtrl.signal
      })
      if (!response.ok) {
        this.writing = false
        this.$message.error(response.statusText)
        return
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let done = false
      const curAiMsg = ''

      // eslint-disable-next-line require-atomic-updates
      textOutputSi = setInterval(() => {
        if (this.writing) {
          if (textStacks.length > 0) {
            this.writingText += textStacks.shift()
            this.scrollBottom()
          }
        } else {
          if (textStacks.length > 0) {
            this.writingText += textStacks.join('')
            textStacks = []
            this.scrollBottom()
          }
          clearInterval(textOutputSi)
          textOutputSi = 0
          if (this.writingText) {
            this.taskList[this.activeIndex].response = this.writingText
            this.taskList[this.activeIndex].state = 1
            if (this.taskRunning) {
              setTimeout(() => {
                this.startAllTask()
              }, 1000)
            }
          }
          this.writingText = ''
          this.scrollBottom()
        }
      }, 25)

      while (!done) {
        this.scrollBottom()
        const { value, done: readerDone } = await reader.read()
        if (value) {
          const char = decoder.decode(value)
          if (char === '\n' && curAiMsg.endsWith('\n')) {
            continue
          }
          if (char) {
            if (char.indexOf('[error]') !== -1) {
              this.writing = false
              this.writingText = ''
              this.taskRunning = false
              const error = char.replace('[error]', '')
              if (error.indexOf('请登录') !== -1) {
                this.$emit('showLogin')
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else if (error.indexOf('请充值') !== -1) {
                if (this.isGpt4(this.ai)) {
                  if (this.priceSetting.text40_vip) {
                    this.$emit('showPay', 'vip')
                  } else {
                    this.$emit('showPay', 'point')
                  }
                } else {
                  this.$emit('showPay', 'vip')
                }
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else {
                this.$alert(error, '系统提示')
              }
              break
            }
            this.writing = true
            for (var i = 0; i < char.length; i++) {
              textStacks.push(char[i])
            }
          }
        }
        done = readerDone
      }
      this.writing = false
    },
    copyText() {
      const _this = this
      const content = this.taskList[this.activeIndex].response
      _this.$copyText(content).then(
        function() {
          _this.$message.success(_this.$lang('已复制'))
        },
        function() {
          _this.$message.error(_this.$lang('复制失败，请重试'))
        }
      )
    },
    retry() {
      this.writing = false
      this.writingText = ''
      this.taskList[this.activeIndex].response = ''
      this.taskList[this.activeIndex].state = 0
      this.sendText()
    },
    stopFetch() {
      const writingText = this.writingText
      this.writing = false
      this.taskRunning = false
      fetchCtrl.abort()
      setTimeout(() => {
        if (!writingText) {
          this.taskList[this.activeIndex].response = ''
          this.taskList[this.activeIndex].state = 0
        }
      }, 50)
    },
    trim(str) {
      if (str) {
        str = str.replace(/(^\s*)|(\s*$)/g, '')
      }
      return str
    },
    textFormat(str) {
      str = this.trim(str)
      if (str && typeof str.replaceAll === 'function') {
        str = str.replaceAll(' ', '&nbsp').replaceAll('<', '&lt;').replaceAll('>', '&gt;').replaceAll('\n', '<br>')
      }
      return str
    },
    scrollBottom() {
      this.$nextTick(() => {
        if (this.$refs['chat-list']) {
          const container = this.$refs['chat-list'].wrap
          setTimeout(() => {
            container.scrollTop = container.scrollHeight
          }, 200)
        }
      })
    },
    scrollTaskToView() {
      this.$nextTick(() => {
        if (this.$refs['task-list']) {
          const container = this.$refs['task-list'].wrap
          setTimeout(() => {
            const rowHeight = 48
            const offset = rowHeight * 3
            const activeTop = this.activeIndex * rowHeight
            const scrollTop = parseInt(container.scrollTop)
            const boxHeight = container.offsetHeight
            if (activeTop < scrollTop) {
              container.scrollTop = activeTop - offset
            } else if (activeTop - boxHeight + rowHeight > scrollTop) {
              container.scrollTop = activeTop - boxHeight + offset
            }
          }, 200)
        }
      })
    },
    startAllTask() {
      if (this.writing) {
        this.$message.error('已有任务正在运行中')
        return
      }
      const taskList = this.taskList
      for (let i = 0; i < taskList.length; i++) {
        if (taskList[i].state === 0) {
          this.switchTask(i)
          this.scrollTaskToView()
          this.taskRunning = true
          this.taskList[i].response = ''
          this.sendText()
          return
        }
      }
      this.taskRunning = false
      this.$message.success('所有任务已执行完毕')
    },
    toBack() {
      if (this.writing) {
        this.$confirm('有任务正在进行中，离开将会中断任务执行，确定返回吗?', '提示', {
          confirmButtonText: '确定返回',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(res => {
          this.$emit('close')
        })
      } else {
        this.$emit('close')
      }
    },
    openSetting() {
      this.$emit('showBatchForm', this.batch_id)
    },
    getTaskList() {
      getTaskList({
        batch_id: this.batch_id
      }).then(res => {
        this.taskList = res.data
        if (this.taskList.length > 0) {
          this.switchTask(0)
        }
      }).catch(err => {
        if (err.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    switchTask(index) {
      if (this.writing) {
        this.$message.error('任务执行中，请停止后切换')
        return
      }
      this.activeIndex = index
    },
    tableIndex(index) {
      index = index + 1
      if (index < 10) {
        index = '0' + index
      }
      return index
    },
    isGpt4(name) {
      return ['openai4', 'diy42', 'diy43', 'azure_openai4', 'wenxin4', 'hunyuan4', 'zhipu4', 'claude2'].includes(name)
    },
    showTaskForm(index = null) {
      if (index === null) {
        this.taskForm = {
          batch_id: this.batch_id,
          message: ''
        }
      } else {
        if (this.taskRunning && this.activeIndex === index) {
          this.$message.error('请先停止任务')
          return
        }
        const task = this.taskList[index]
        this.taskForm = {
          batch_id: this.batch_id,
          task_id: task.id,
          message: task.message
        }
      }
    },
    closeTaskForm() {
      this.taskForm = null
    },
    delTask(index) {
      if (this.taskRunning && this.activeIndex === index) {
        this.$message.error('请先停止任务')
        return
      }
      const task_id = this.taskList[index].id
      this.$confirm('删除后不可恢复，确认吗?', '提示', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delTask({
          batch_id: this.batch_id,
          task_id: task_id
        }).then(() => {
          if (this.activeIndex === this.taskList.length - 1) {
            this.activeIndex--
          }
          this.taskList.splice(index, 1)
        }).catch(err => {
          if (err.errno === 403) {
            this.$emit('showLogin')
          }
        })
      })
    },
    getBatchInfo() {
      getBatchInfo({
        batch_id: this.batch_id
      }).then(res => {
        this.batch = res.data
      }).catch(err => {
        if (err.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    saveTask() {
      saveTask(this.taskForm).then(res => {
        this.$message.success(res.message)
        if (!this.taskForm.task_id) {
          this.taskList.push({
            id: res.data.task_id,
            message: this.taskForm.message,
            response: '',
            total_tokens: 0,
            state: 0
          })
          if (this.activeIndex === -1) {
            this.activeIndex = 0
          }
        } else {
          this.taskList[this.activeIndex].message = this.taskForm.message
        }

        this.closeTaskForm()
      }).catch(err => {
        if (err.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    showImportForm() {
      if (this.taskRunning) {
        this.$message.error('请先停止任务')
        return
      }
      this.importForm = {}
    },
    closeImportForm() {
      this.importForm = null
    },
    checkExcelFile(field) {
      if (field.type !== 'application/vnd.ms-excel' && field.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        this.$message.error(this.$lang('请上传Excel文件'))
        return false
      }
    },
    importTaskSuccess(res, file, fileList) {
      if (res.errno > 0) {
        this.$message.error(res.message)
        return
      }
      this.getTaskList()
      this.closeImportForm()
      this.$message.success(res.message)
    },
    delAllTask() {
      this.$confirm('删除后不可恢复，确认吗?', '提示', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delAllTask({
          batch_id: this.batch_id
        }).then(res => {
          this.activeIndex = -1
          this.taskList = []
          this.$message.success(res.message)
        })
      }).catch(err => {
        if (err.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    exportDocx() {
      const task = this.taskList[this.activeIndex]
      let html = '<div style="font-size: 12pt;">' + this.textToHtml(task.response) + '</div>'
      if (task.message.length <= 50) {
        html = '<p><center><h2>' + task.message + '</h2></center></p>' + html
      }
      const contentBlob = htmlDocx.asBlob(html)
      fileSaver.saveAs(contentBlob, task.message.substr(0, 30) + '.docx')
    },
    exportZip() {
      const zip = new jszip()
      let files = 0
      this.zipExporting = true
      this.$loading({
        fullscreen: true,
        lock: true,
        text: '正在打包',
        background: 'rgba(0,0,0,0.3)',
        customClass: 'my-loading'
      })
      this.$nextTick(() => {
        for (var i = 0; i < this.taskList.length; i++) {
          const task = this.taskList[i]
          if (task.state !== 1 || !task.response) {
            continue
          }
          this.zipExportHtml = task.response
          let html = '<div style="font-size: 12pt;">' + this.textToHtml(task.response) + '</div>'
          if (task.message.length <= 50) {
            html = '<p><center><h2>' + task.message + '</h2></center></p>' + html
          }
          zip.file(task.message.substr(0, 30) + '.docx', htmlDocx.asBlob(html))
          files++
        }
        this.zipExporting = false
        this.$loading().close()
        if (files === 0) {
          this.$message.error('请先生成内容')
          return
        }
        zip.generateAsync({ type: 'blob' }).then(content => {
          fileSaver.saveAs(content, 'data.zip')
        })
      })
    },
    textToHtml(str) {
      str = this.trim(str)
      if (str && typeof str.replaceAll === 'function') {
        str = str.replaceAll('  ', '&nbsp;&nbsp;').replaceAll('<', '&lt;').replaceAll('>', '&gt;').replaceAll('\n', '<br>')
      }
      return str
    }
  }
}
</script>

<style lang="scss">
.scrollbar-wrapper {
  height: 100%;
  overflow-x: hidden;
}
</style>
