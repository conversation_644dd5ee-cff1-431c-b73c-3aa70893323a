<template>
  <div class="welcome box-msg-list" :class="{'style-chat': module === 'chat' || module === 'cosplay', 'style-write': module === 'write' || module === 'draw'}">
    <el-scrollbar ref="msglist" wrap-class="scrollbar-wrapper">
      <div class="list">
        <div class="row row-ai">
          <div class="message">
            <div class="avatar">
              <img src="/static/img/ic_ai.png">
            </div>
            <div class="text markdown-body" style="min-width: 480px; max-width: 600px;">
              <div class="title" v-if="welcomeTitle">{{ welcomeTitle }}</div>
              <div class="desc" v-if="welcomeDesc" v-html="welcomeDesc"></div>
              <div class="tips" v-if="welcomeTips.length > 0">
                <ul>
                  <li v-for="item in welcomeTips" @click="quickMessage(item)" v-if="item">
                    <span>{{ item }}</span>
                    <i class="el-icon-position"></i>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
export default {
  props: {
    module: {
      type: String,
      default: 'chat'
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    tips: {
      type: String,
      default: ''
    },
    hasModel4: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    welcomeTitle() {
      if (!this.title) {
        return ''
      }
      if (this.module === 'chat') {
        return this.$lang('你好！我是') + this.title;
      } else if (this.module === 'cosplay') {
        return this.$lang('我是') + this.title;
      }
      return this.title;
    },
    welcomeDesc() {
      if (this.desc) {
        return this.desc.replaceAll("\n", "<br>")
      } else {
        if(this.module === 'cosplay') {
          return this.$lang('请输入你的问题')
        }
      }
      return ''
    },
    welcomeTips() {
      if (this.tips) {
        return this.tips.split("\n")
      } else {
        return []
      }
    }
  },
  methods: {
    quickMessage(text) {
      this.$emit('use', text)
    }
  }
}
</script>
<style lang="scss" scoped>
.welcome {
  width: 768px;
  max-width: 100%;

  .title {
    color: #222;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 1px;
    line-height: 32px;
    transition: color 0.1s ease-in-out;
  }
  .desc {
    margin-bottom: 10px !important;
  }

  .tips {
    padding: 10px;

    ul, li {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    li {
      background: #eff0f0;
      border-radius: 4px;
      margin-bottom: 13px;
      padding: 12px 15px;
      font-size: 14px;
      color: #444;
      line-height: 16px;
      transition: background 0.1s ease-in-out;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;

      i {
        opacity: 0;
        margin-left: 10px;
        transition: all 0.2s ease-in-out;
      }

      &:hover, &:active {
        background: #e5e7eb;
        color: #222;
        i {
          opacity: 1;
        }
      }
    }
  }
}
</style>
