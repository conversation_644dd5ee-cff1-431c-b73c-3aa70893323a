<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      :title="'创作模型' | lang"
      width="1000px"
      height="600px"
      :visible="true"
      :close-on-click-modal="false"
      :append-to-body="true"
      :before-close="closeForm"
    >
      <div>
        <div class="label">{{ '类别：' | lang }}</div>
        <ul>
          <li>全部</li>
        </ul>
      </div>
      <div>
        <div v-for="topic in topicList" class="group-write">
          <div v-if="topic.prompts && topic.prompts.length > 0 && !topic.hidden" class="topic-title">
            <span>{{ topic.title }}</span>
          </div>
          <div v-if="topic.prompts && topic.prompts.length > 0 && !topic.hidden" class="prompt-list">
            <div v-for="prompt in topic.prompts" class="prompt-item" :class="{active: activePromptId === prompt.id, hidden: prompt.hidden}" @click="changePrompt(prompt.id)">
              <div class="prompt-title">{{ prompt.title }}</div>
              <div class="prompt-desc">{{ prompt.desc }}</div>
              <div class="prompt-ops">
                <div class="op-item" :title="'点击量' | lang">
                  <svg-icon class="icon" icon-class="ic_view" />
                  {{ prompt.views }}
                </div>
                <div class="op-item" :title="'使用量' | lang">
                  <svg-icon class="icon" icon-class="ic_usage" />
                  {{ prompt.usages }}
                </div>
                <div class="op-item" :title="'收藏' | lang" @click.stop="doPromptVote(prompt.id)">
                  <svg-icon v-if="prompt.isVote === 1" class="icon" icon-class="ic_vote_active" />
                  <svg-icon v-else class="icon" icon-class="ic_vote" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    topicList: {
      type: Array,
      default: () => {
        return []
      }
    },
    activePromptId: 0
  },
  data() {
    return {
      form: null,
      formRules: {
        title: [
          { required: true, message: '此项必填', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.form = this.group
  },
  methods: {
    closeForm() {
      this.$emit('close')
    },
    doSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
        } else {
          this.$message.error('请填写必填项')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .tips {
    margin-left: 10px;
    color: #999;
  }
</style>
