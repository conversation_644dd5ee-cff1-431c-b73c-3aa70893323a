<template>
  <div>
    <el-dialog
      custom-class="my-dialog pay-dialog"
      title=""
      :visible="true"
      width="770px"
      :close-on-click-modal="false"
      :before-close="closePay"
      :append-to-body="true"
    >
      <div class="tabs">
        <div class="tab-item" :class="{active: type === 'vip'}" @click="switchType('vip')">{{ '开通VIP会员' | lang }}</div>
        <div class="tab-item" :class="{active: type === 'point'}" @click="switchType('point')">{{ '充值' | lang }}{{ priceSetting.title }}</div>
        <div class="tab-item" :class="{active: type === 'card'}" @click="switchType('card')">{{ '卡密兑换' | lang }}</div>
      </div>
      <div class="box-pay">
        <div v-if="type === 'card'" class="box-card">
          <div>
            <el-input ref="codeInput" v-model="code" type="text" size="large" class="input-code" :placeholder="'请输入卡密' | lang" clearable>
              <el-button slot="append" size="large" @click="getCardInfo">{{ '查询' | lang }}</el-button>
            </el-input>
          </div>
          <div style="padding: 20px 0;">
            <div class="form-item">
              <span class="label">{{ '面额' | lang }}</span>
              <span v-if="!card" class="value">-</span>
              <span v-else-if="card.type === 'point'" class="value">{{ card.val }} {{ priceSetting.title || '积分' }}</span>
              <span v-else-if="card.type === 'vip'" class="value">vip会员{{ card.val }}个月</span>
            </div>
            <div class="form-item">
              <span class="label">{{ '状态' | lang }}</span>
              <span v-if="!card" class="value">-</span>
              <span v-else-if="card.bind_time" class="value text-red">{{ card.bind_time }} 已使用</span>
              <span v-else class="value text-green">{{ '未使用' | lang }}</span>
            </div>
          </div>
          <div v-if="card && !card.bind_time">
            <el-button class="btn-pay" type="primary" @click="bindCard">{{ '确定兑换' | lang }}</el-button>
          </div>
        </div>
        <div v-if="goodsList && goodsList.length > 0">
          <div class="goods-list">
            <div v-for="(item, index) in goodsList" class="goods" :class="{active: item.id === goods_id}" @click="chooseGoods(item.id)">
              <div v-if="item.hint" class="hint">{{ item.hint }}</div>
              <div class="title">
                <span class="num">{{ item.num }}</span>
                <span v-if="type === 'vip'" class="unit">{{ '个月' | lang }}</span>
                <span v-if="type === 'point'" class="unit">{{ priceSetting.title || '积分' }}</span>
              </div>
              <div class="price">
                <span class="unit">{{ '￥' | lang }}</span>{{ item.price / 100 }}
              </div>
              <div v-if="item.market_price > 0" class="market_price">{{ '￥' | lang }}{{ item.market_price / 100 }}</div>
            </div>
            <div style="clear: both;" />
          </div>
          <div class="info">
            <div class="pay">
              <div v-if="pay_url" class="box-qrcode">
<!--                <div class="pay-type">
                  <div class="item">微信支付</div>
                  <div class="item">支付宝</div>
                </div>-->
                <div class="qrcode">
                  <vue-qr :text="pay_url" :size="160" />
                </div>
                <div class="qrcode-desc" style="text-align: center;">
                  <div class="title">{{ '请支付' | lang }}</div>
                  <div class="price">
                    <span class="unit">{{ '￥' | lang }}</span>
                    <span>{{ totalFee / 100 }}</span>
                  </div>
                  <div class="paytype">
                    <svg-icon class="icon" icon-class="ic_wxpay" />
                    <span>{{ '微信扫码付款' | lang }}</span>
                  </div>
                </div>
              </div>

              <div v-else>
                <el-button class="btn-pay" type="primary" :disabled="!goods_id" @click="createOrder">{{ '立即支付' | lang }}</el-button>
                <div class="agreement">
                  <el-checkbox v-model="isAgree" color="#666">{{ '请认真阅读并同意' | lang }}<a @click="toDoc('service')">{{ '《服务协议》' | lang }}</a></el-checkbox>
                </div>
              </div>
            </div>

            <div v-if="curGoods && curGoods.desc && curGoods.desc.length > 0" class="desc">
              <ul>
                <li v-for="item in curGoods.desc" v-if="item">
                  <svg-icon class="icon" icon-class="ic_pay_check" />
                  <span>{{ item }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getGoodsList, createOrder, checkPay } from '@/api/order'
import { getCardInfo, bindCard } from '@/api/user'
import { toDoc } from '@/utils/util'
import vueQr from 'vue-qr'

export default {
  components: { vueQr },
  props: {
    payType: {
      type: String,
      default: 'point'
    }
  },
  data() {
    return {
      type: 'point',
      goodsList: [],
      goods_id: 0,
      order_id: '',
      pay_url: '',
      checkPaySi: 0,
      isAgree: true,
      code: '',
      card: null
    }
  },
  computed: {
    ...mapGetters([
      'hasModel4',
      'model4Name',
      'drawIsOpen',
      'videoIsOpen',
      'musicIsOpen',
      'priceSetting'
    ]),
    totalFee() {
      var totalFee = 0
      if (this.goods_id) {
        this.goodsList.forEach((item, index) => {
          if (item['id'] === this.goods_id) {
            totalFee = item['price']
          }
        })
      }
      return totalFee
    },
    curGoods() {
      var curGoods = null
      if (this.goods_id) {
        this.goodsList.forEach((item, index) => {
          if (item['id'] === this.goods_id) {
            curGoods = item
          }
        })
      }
      return curGoods
    }
  },
  watch: {
    code: function() {
      this.card = null
    }
  },
  created() {
    this.type = this.payType
    this.getGoodsList()
  },
  methods: {
    getGoodsList() {
      getGoodsList({ type: this.type }).then(res => {
        const goodsList = res.data
        this.goodsList = goodsList
        if (goodsList && goodsList.length > 0) {
          for (var i = 0; i < goodsList.length; i++) {
            if (goodsList[i].is_default) {
              this.chooseGoods(goodsList[i].id)
              break
            }
          }
        }
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
          this.closePay()
        }
      })
    },
    switchType(type) {
      this.type = type
      this.goods_id = 0
      this.goodsList = null
      this.pay_url = ''
      if (type === 'card') {
        this.$nextTick(() => {
          this.$refs.codeInput.focus()
        })
      } else {
        this.getGoodsList()
      }
    },
    chooseGoods(goods_id) {
      this.goods_id = goods_id
      this.stopCheckPay()
      this.pay_url = ''
    },
    createOrder() {
      if (!this.goods_id) {
        this.$message.error('请选择套餐')
        return
      }
      if (!this.isAgree) {
        this.$message.error('请先阅读并同意《服务协议》')
        return
      }
      createOrder({
        platform: 'web',
        type: this.type,
        goods_id: this.goods_id
      }).then(res => {
        this.order_id = res.data.order_id
        this.pay_url = res.data.pay_url
        this.checkPaySi = setInterval(() => {
          this.checkPay()
        }, 3000)
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    stopCheckPay() {
      if (this.checkPaySi) {
        clearInterval(this.checkPaySi)
        this.checkPaySi = 0
      }
    },
    closePay() {
      this.stopCheckPay()
      this.$emit('close')
    },
    checkPay() {
      checkPay({
        order_id: this.order_id
      }).then(res => {
        if (res.data.ispay) {
          this.$message.success('付款成功')
          window.location.reload()
        }
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    toDoc(type) {
      toDoc(type)
    },
    getCardInfo() {
      if (!this.code) {
        this.$refs.codeInput.focus()
        return
      }
      getCardInfo({
        code: this.code
      }).then(res => {
        this.card = res.data
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    bindCard() {
      if (!this.code) {
        this.$refs.codeInput.focus()
        this.card = null
        return
      }
      bindCard({
        code: this.code
      }).then(res => {
        this.$message.success(res.message)
        this.code = ''
        this.card = null
        this.$store.dispatch('user/getInfo')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tabs {
  width: 100%;
  background: #293141;
  padding-top: 10px;
  padding-left: 30px;
  position: relative;
  top: -1px;
  .tab-item {
    display: inline-block;
    padding: 12px 25px;
    background: #454f64;
    color: #fff;
    cursor: pointer;
    font-size: 15px;
    letter-spacing: 1px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    position: relative;
    z-index: 2;
    margin-right: 10px;
    i,span {
      position: absolute;
      bottom: 0;
      display: none;
      width:20px;
      height:20px;
      background:#454f64;
    }
    i {
      left: -10px;
    }
    span {
      right: -10px;
    }

    &:hover {
      background: #727e96;
    }
    &.active {
      background: linear-gradient(to bottom, #fff9f2, #fff);
      color: #000;
      padding-top: 18px;
      font-weight: bold;
      z-index: 1;
      top: 2px;
      i,span {
        display: block;
        background:#fff;
      }
    }
    &:first-child {
      border-bottom-left-radius: 0 !important;
      i {
        background: #454f64 !important;
      }
    }
    &:last-child {
      border-bottom-right-radius: 0 !important;
    }
  }
}

.box-title {
  width: 100%;
  height: 36px;
  line-height: 36px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  .desc {
    font-weight: normal;
    color: #999;
    font-size: 14px;
    margin-left: 10px;
  }
}
.box-pay {
  padding: 40px 50px 40px 60px;
  box-sizing: border-box;
  min-height: 326px;
  background: #fff;
}
.goods-list {
  .goods {
    background: #f7f7f9;
    width: 150px;
    height: 120px;
    text-align: center;
    padding: 15px 0;
    float: left;
    margin-right: 15px;
    box-sizing: border-box;
    margin-bottom: 15px;
    cursor: pointer;
    border-radius: 10px;
    position: relative;

    .hint {
      background: #fb6a32;
      color: #fff;
      position: absolute;
      top: -12px;
      left: 0;
      font-size: 13px;
      padding: 4px 10px;
      border-radius: 4px;
    }

    .title {
      font-size: 15px;
      display: flex;
      align-items: baseline;
      justify-content: center;
      color: #333;
      margin-top: 5px;
      .unit {
        font-weight: normal;
        margin-left: 3px;
      }
    }
    .price {
      font-size: 32px;
      margin: 10px 0;
      font-weight: bold;
      color: #444e63;
      .unit {
        font-size: 14px;
        font-weight: normal;
        position: relative;
        top: -2px;
        margin-right: 3px;
      }
    }
    .market_price {
      color: #999;
      text-decoration: line-through;
      font-size: 14px;
    }
    &.active {
      background: #fadfbb;
      .price {
        color: #522a19;
      }
      .market_price {
        color: #a88e7d;
      }
    }
  }
}
.info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  .desc {
    width: 340px;
    ul,li {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    li {
      float: left;
      width: 100%;
      line-height: 22px;
      display: flex;
      align-items: flex-start;
      font-size: 15px;
      color: #522a19;
      margin: 8px 0;
      letter-spacing: 1px;
      .icon {
        font-size: 20px;
        min-width: 20px;
        margin-right: 10px;
        color: #f6a944;
      }
    }
  }
  .pay {
    width: 300px;
    .btn-pay {
      background-color: #454f64 !important;
      border: none;
      margin-top: 15px;
      &:hover {
        opacity: 0.8;
      }
    }
    .agreement {
      width: 100%;
      font-size: 14px;
      line-height: 32px;
      margin-top: 15px;
      color: #999;
    }
    .box-qrcode {
      margin: 10px 20px 15px 0;
      border-radius: 10px;
      display:flex;
      .qrcode {
        text-align: center;
        border-radius: 10px;
        overflow: hidden;
        width: 140px;
        height: 140px;
        margin-right: 15px;

        img {
          position: relative;
          left: -10px;
          top: -10px;
        }
      }
      .qrcode-desc {
        text-align: center;
        padding: 15px 0;
        box-sizing: border-box;
        .title {
          font-size: 16px;
          color: #666;
        }
        .price {
          font-size: 36px;
          color: #fa554c;
          font-weight: bold;
          margin: 13px 0;
          .unit {
            font-size: 14px;
            margin-right: 5px;
            position:relative;
            top: -2px;
          }
        }
        .paytype {
          display: flex;
          align-items: center;
          font-size: 14px;
          margin-top: 20px;
          color: #666;
          .icon {
            font-size: 16px;
            margin-right: 5px;
            color: #56c505;
          }
        }
      }

    }
  }
  .el-radio {
    margin-right: 0;
  }
}

.box-card {
  width: 360px;
  margin: 20px auto 0 auto;
  text-align: center;
  .input-code {
    width: 100%;
    font-size: 16px;
    border: 1px solid #10a37f;
    outline: none;
    border-radius: 4px;
    .el-input__inner {
      outline: none;
    }
  }
  .form-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f7f7f7;
    padding: 10px 0;
    .label {
      font-size: 14px;
      position: relative;
      line-height: 24px;
      color: #758b97;
    }
    .value {
      color: #333;
      font-size: 14px;
      font-weight: bold;
    }
    .text-red {
      color: #d81e06;
    }
    .text-green {
      color: #06b13c;
    }
  }
}

</style>
<style lang="scss">
.box-pay {
  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #293141 !important;
    border-color: #293141 !important;
  }
  .el-checkbox__input.is-checked+.el-checkbox__label {
    color: #606266;
  }
  .el-checkbox__inner:hover {
    border-color: #10a37f;
  }
}
</style>
