<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      title="视频内容"
      :visible="true"
      width="760px"
      :show-close="true"
      :append-to-body="true"
      :before-close="closeVideo"
    >
      <div class="container">
        <div class="video">
          <video
            :src="video.video"
            :poster="video.poster"
            class="uni-video-video"
            loop
            autoplay
            playsinline
            controls
            disablepictureinpicture
            controlslist="nodownload noplaybackrate"
            play-btn-position="center"
            style="object-fit: contain;"
          />
        </div>
        <div class="tools">
          <div class="btn-group">
            <el-button size="mini" type="primary" icon="el-icon-document-copy" title="复用参数，生成同款视频" @click="handleVideo('copy')">生成同款</el-button>
            <el-button size="mini" type="default" icon="el-icon-edit-outline" title="编辑本视频的内容" @click="handleVideo('edit')">编辑视频</el-button>
            <el-button size="mini" type="default" icon="el-icon-time" title="将视频延长4秒" @click="handleVideo('extend')">延长4s</el-button>
            <!--<el-button size="mini" type="default" icon="el-icon-rank" title="无损放大本视频" @click="handleVideo('upscale')">无损放大</el-button>-->
          </div>
          <span v-if="video.seed" title="种子值 --seed" style="font-size: 12px; color: #898989; float: right;"><svg-icon class="icon" icon-class="ic_seed" /> {{ video.seed }}</span>
        </div>
        <div class="param">
          <div class="prompt">提示词：{{ video.prompt }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    video: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods: {
    handleVideo(action) {
      this.$emit('handle', action, this.video.video_id)
    },
    closeVideo() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.video {
  width: 720px;
  height: 406px;
  text-align: center;
  background: #1f1f1f;
  border-radius: 5px;
  margin-top: -15px;
  video {
    height: 100%;
    max-width: 100%;
  }
}

.tools {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
  .btn-group {
    display: flex;
    align-items: center;
  }
}

.param {
  padding: 15px 0;
  .prompt {
    font-size: 14px;
    line-height: 24px;
    color: #666;
    padding: 10px 15px;
    background: #f7f7f8;
    border-radius: 5px;

    .icon {
      margin-right: 5px;
      display: inline;
    }
  }
}

</style>
