<template>
  <div style="margin-bottom: 10px !important;">
    <div v-if="item.state === 0" class="writing">
      <video-loading :title="'生成中，预计需要1-3分钟左右' | lang" color="#04BABE" text-color="#999" />
    </div>
    <div v-else-if="item.state === 1">
      <div class="video">
        <video
          :src="item.video"
          :poster="item.poster"
          class="uni-video-video"
          loop
          preload="none"
          playsinline
          controls
          disablepictureinpicture
          controlslist="nodownload noplaybackrate"
          play-btn-position="center"
          style="object-fit: contain;"
        ></video>
        <span class="btn-download" title="下载视频" @click.stop="download(item.video)"><i class="el-icon-download" /></span>
      </div>
    </div>
    <div v-else-if="item.state === 2" class="writing fail">
      <div class="errmsg">
        <i class="el-icon-error" />
        <span style="max-width: 460px;">{{ '生成失败' | lang }}: {{ item.errmsg }} </span>
      </div>
      <div><span class="btn-retry" @click="retry(item.video_id)">{{ '重新生成' | lang }}</span></div>
    </div>
  </div>
</template>

<script>
import videoLoading from './videoLoading'
import fileSaver from 'file-saver'
export default {
  components: { videoLoading },
  props: {
    item: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods: {
    previewImage(image) {
      this.$emit('preview', image)
    },
    retry(video_id) {
      this.$emit('retry', video_id)
    },
    download(video) {
      fileSaver.saveAs(video, 'video.mp4')
    }
  }
}
</script>
<style lang="scss" scoped>
.video {
  width: 720px;
  height: 406px;
  box-sizing: border-box;
  transition: all 0.1s;
  position: relative;
  background: #1f1f1f;
  overflow: hidden;
  border-radius: 5px;
  text-align: center;

  video {
    max-width: 720px;
    max-height: 406px;
  }
  .el-image {
    float: left;
  }

  .btn-download {
    display: block;
    padding: 3px 10px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    text-align: center;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 20px;
    border-radius: 10px;
    z-index: 2;
    &:hover {
      background: rgba(0, 0, 0, 1)
    }
  }
  &:hover {
    opacity: 0.9;
    .ctrls {
      display: flex;
    }
  }
  &.small {
    width: 256px;
  }
}

.writing {
  width: 720px;
  height: 406px;
  background: #eff0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.fail {
  flex-direction: column;
}

.fail .errmsg {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  line-height: 22px;
  padding: 15px 0;
  overflow: hidden;
  max-height: 105px;
}
.fail .errmsg i {
  font-size: 18px;
  margin-right: 5px;
  position: relative;
  top: 3px;
  color: #dd0000;
}
.fail .btn-retry {
  color: #666;
  padding: 5px 12px;
  background: #fff;
  border-radius: 5px;
  margin-top: 10px;
  font-size: 12px;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
</style>
