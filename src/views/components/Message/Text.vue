<template>
  <div @click="onClick" v-html="message" />
</template>

<script>
import MarkdownIt from 'markdown-it'
import mdKatex from '@traptitech/markdown-it-katex'
import mila from 'markdown-it-link-attributes'
import hljs from 'highlight.js'
import { Base64 } from 'js-base64'

const mdi = new MarkdownIt({
  linkify: true,
  highlight(code, language) {
    code = code.replace('![cursor](/static/img/cursor.gif)', '')
    const codeSource = Base64.encode(code)
    const lineLen = code.split(/\r|\n/ig).length
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      code = hljs.highlight(code, { language: language }).value
    } else {
      code = hljs.highlightAuto(code).value
    }

    // 添加行号
    let lineStr = `<ul class="code-linenum">`
    for (let i = 0; i < lineLen - 1; i++) {
      lineStr += `<li class="code-linenum-line">${i + 1}</li>`
    }
    lineStr += `</ul>`
    code = lineStr + code

    return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${language.toUpperCase()}</span><a class="code-block-header__lang" style="margin-left: 15px;" data-action="copy" data-code="${codeSource}">` + (window.$lang === 'zh-cn' ? '复制' : 'copy') + `</a></div><code class="hljs code-block-body ${language}">${code}</code></pre>`
  }
})

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' }})
mdi.use(mdKatex, { blockClass: 'katexmath-block rounded-md p-[10px]', errorColor: ' #cc0000' })

export default {
  props: {
    text: {
      type: String,
      default: ''
    },
    writing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      code: ''
    }
  },
  computed: {
    message() {
      var text = this.trim(this.text)
      if (this.writing) {
        text += '![cursor](/static/img/cursor.gif)'
      }
      return mdi.render(text)
    }
  },
  methods: {
    trim(str) {
      return str.replace(/(^\s*)|(\s*$)/g, '')
    },
    onClick(e) {
      const _this = this
      const action = e.target.dataset.action
      if (action === 'copy') {
        const code = e.target.dataset.code
        _this.$copyText(Base64.decode(code)).then(
          function() {
            _this.$message.success('已复制')
          },
          function() {
            _this.$message.error('复制失败，请重试')
          }
        )
      }
    }
  }
}
</script>
