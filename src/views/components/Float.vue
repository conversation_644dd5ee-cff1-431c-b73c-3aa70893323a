<template>
  <div class="float">
    <div class="btn" @click="toDoc('kefu')">
      <svg-icon icon-class="ic_kefu" class="icon"></svg-icon>
      <span>{{ '客服' | lang }}</span>
    </div>
    <div class="btn" @click="toDoc('help')">
      <svg-icon icon-class="ic_doc" class="icon" style="font-size: 18px;"></svg-icon>
      <span>{{ '教程' | lang }}</span>
    </div>
    <div v-if="notice" class="btn" @click="openNotice">
      <svg-icon icon-class="ic_notice" class="icon" style="font-size: 18px;"></svg-icon>
      <span>{{ '公告' | lang }}</span>
    </div>
  </div>
</template>

<script>
import { toDoc } from '@/utils/util'
export default {
  props: {
    notice: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toDoc(type) {
      toDoc(type)
    },
    openNotice() {
      this.$emit('openNotice')
    }
  }
}
</script>
<style lang="scss" scoped>
.float {
  position: fixed;
  right: 0;
  top: 50%;
  margin-top: -120px;
  overflow: hidden;
  font-size: 14px;
  background: none;
  z-index: 9;
  .btn {
    width: 32px;
    min-height: 88px;
    padding: 15px 8px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.1s ease-in-out, color 0.1s ease-in-out;
    margin: 10px 0;
    writing-mode: tb-rl;
    flex-direction: row;
    letter-spacing: 2px;

    .icon {
      font-size: 20px;
      margin-bottom: 5px;
    }
    span {
      text-align: center;
    }
  }

}
</style>
