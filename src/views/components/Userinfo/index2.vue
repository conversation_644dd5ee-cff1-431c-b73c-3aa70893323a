<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      :title="'个人中心' | lang"
      :visible="true"
      width="500px"
      :close-on-click-modal="true"
      :before-close="closeUserInfo"
    >
      <div class="user-container">
        <div class="box-user">
          <div class="userinfo">
            <div class="avatar">
              <img :src="userinfo.avatar || '/static/img/no_avatar.png'">
            </div>
            <div class="info">
              <div class="nickname">
                <span>{{ userinfo.nickname || $lang('无昵称') }}</span>
              </div>
              <div class="mid">ID: {{ userinfo.user_id }}</div>
            </div>
          </div>
          <div class="btn-setting" :title="'编辑资料' | lang" @click="showSetting">
            <i class="icon el-icon-s-tools" />
          </div>
        </div>
        <div class="box-wallet">
          <div class="vip" @click="showPay('vip')">
            <img class="icon" src="@/assets/ic_vip.png">
            <div>
              <p class="title">{{ 'VIP会员' | lang }}</p>
              <p v-if="userinfo.vip_expire_time" class="desc">{{ userinfo.vip_expire_time }} {{ '到期' | lang }}</p>
              <p v-else class="desc">{{ '高速通道 无限对话' | lang }}</p>
            </div>
          </div>
          <div class="point" @click="showPay('point')">
            <div>
              <span class="num">{{ userinfo.balance_point }}</span>
            </div>
            <div class="label">{{ priceSetting.title }}{{ '余额' | lang }}</div>
          </div>
        </div>

        <div class="logout">
          <el-button type="danger" size="mini" @click="logout">{{ '退出登录' | lang }}</el-button>
          <div class="links">
            <a @click="toDoc('service')">{{ '《服务协议》' | lang }}</a>
            <a @click="toDoc('privacy')">{{ '《隐私政策》' | lang }}</a>
            <a @click="toDoc('legal')">{{ '《免责声明》' | lang }}</a>
          </div>
        </div>
      </div>
    </el-dialog>
    <setting v-if="settingShow" :avatar="userinfo.avatar" :nickname="userinfo.nickname" @close="closeSetting" @success="freshUserInfo" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getInfo } from '@/api/user'
import { toDoc } from '@/utils/util'
import setting from './setting'
export default {
  name: 'Userinfo',
  components: { setting },
  data() {
    return {
      userinfo: {
        balance_point: 0
      },
      settingShow: false
    }
  },
  computed: {
    ...mapGetters([
      'hasModel4',
      'model4Name',
      'drawIsOpen',
      'videoIsOpen',
      'musicIsOpen',
      'priceSetting'
    ])
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      getInfo().then(res => {
        this.userinfo = res.data
      })
    },
    freshUserInfo() {
      this.$store.dispatch('user/getInfo')
      this.getUserInfo()
    },
    closeUserInfo() {
      this.$emit('close')
    },
    showPay(type) {
      this.$emit('showPay', type)
    },
    showSetting() {
      this.settingShow = true
    },
    closeSetting() {
      this.settingShow = false
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.closeUserInfo()
      window.location.reload()
    },
    toDoc(type) {
      toDoc(type)
    }
  }
}
</script>

<style lang="scss" scoped>
  .user-container {
    border-radius: 10px;
    overflow: hidden;
    padding: 0 30px;
    position: relative;
    top: -10px;
    .box-user {
      position: relative;
      width: 100%;
      left: 0;
      top: 0;
      height: 90px;
      box-sizing: content-box;
      z-index: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .btn-setting {
        font-size: 20px;
        cursor: pointer;
        padding: 8px;
        width: 20px;
        height: 20px;
        border-radius: 5px;
        color: #666;
        &:hover {
          background: #eff0f0;
        }
      }

      .userinfo {
        display: flex;
        align-items: flex-start;
        width: 300px;

        .avatar {
          width: 50px;
          height: 50px;
          border-radius: 5px;
          overflow: hidden;
          background-color: #f8f8f8;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .info {
          display: flex;
          flex-direction: column;
          margin-left: 15px;
          .nickname {
            font-size: 15px;
            font-weight: bold;
            margin-top: 4px;
            span {
              margin-right: 10px;
            }
            .icon {
              cursor: pointer;
            }
          }
          .mid {
            font-size: 14px;
            line-height: 26px;
            margin-top: 6px;
          }
          .phone {
            color: #fff;
            background-color: rgba(0, 0, 0, 0.2);
            padding: 2px 4px 2px 2px;
            height: 18px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            img {
              width: 14px;
              height: 14px;
            }
            span {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .box-wallet {
    width: 400px;
    height: 88px;
    margin: 5px auto;
    border: none;
    position: relative;
    //background: linear-gradient(30deg, #f7debe, #f4c384);
    background: #fadfbb;
    border-radius: 10px;
    color: #9a5b12;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 10px;

    .vip {
      display: flex;
      align-items: flex-start;
      padding: 10px 15px;
      border-radius: 10px;
      cursor: pointer;
      &:hover, &:active {
        background: rgba(255, 255, 255, 0.2);
      }
      .icon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
      }
      .line {
        margin: 0 10px;
        vertical-align: middle;
        display: inline-block;
        width: 1px;
        height: 12px;
        background: rgba(154, 91, 18, 0.4);
      }
      p {
        margin: 0;
      }
      .title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
      }
      .desc {
        font-size: 14px;
        line-height: 20px;
        margin-top: 3px;
      }
      .btn-vip {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 72px;
        height: 24px;
        background: #ffe8b5;
        border-radius: 12px;
        font-size: 12px;
        padding: 0;
        border: none;
        color: #9a5b12;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
      }
    }
    .point {
      text-align: center;
      line-height: 24px;
      font-size: 13px;
      padding: 10px 0;
      width: 120px;
      box-sizing: border-box;
      cursor: pointer;
      border-radius: 10px;
      &:hover, &:active {
        background: rgba(255, 255, 255, 0.2);
      }
      .num {
        font-size: 20px;
        //color: #10a37f;
        color: #9a5b12;
        font-weight: bold;
        letter-spacing: 1px;
        white-space: nowrap;
      }
      .label {
        font-size: 14px;
        letter-spacing: 1px;
      }
    }
  }

  .logout {
    text-align: center;
    margin-top: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .links {
      width: 260px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      a {
        color: #999;
        &:hover {
          color: #10a37f;
        }
      }
    }
  }

</style>

