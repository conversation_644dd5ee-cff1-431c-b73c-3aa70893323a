<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      title=""
      :visible="true"
      width="320px"
      :before-close="closeForm"
    >
      <div class="container">
        <div class="title-container">{{ '绑定微信账号' | lang }}</div>
        <div class="qrcode" v-if="bindQrcode">
          <img :src="bindQrcode" />
          <div style="letter-spacing: 1px; margin-top: 5px;">
            <svg-icon class="icon" icon-class="ic_wechat" style="color: #10a37f; margin-right: 5px;" />
            <span>{{ '使用微信扫码' | lang }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { bindWechat } from '@/api/user'
import { getQrcode} from '@/api/login'

export default {
  data() {
    return {
      bindQrcode: '',
      checkSi: 0
    }
  },
  created() {
    this.getQrcode()
  },
  methods: {
    closeForm() {
      clearInterval(this.checkSi)
      this.$emit('close')
    },
    getQrcode() {
      getQrcode({
        type: 'bind'
      }).then(res => {
        this.bindQrcode = res.data.qrcode
        if (this.checkSi) {
          clearInterval(this.checkSi)
        }
        this.checkSi = setInterval(() => {
          bindWechat({
            code: res.data.code
          }).then(res => {
            if (res.data.success === 1) {
              this.$message.success(res.message)
              this.$emit('success')
              this.closeForm()
            } else if (res.data.success === 2) {
              this.$message({
                message: res.message,
                type: 'error',
                duration: 5000
              });
            }
          })
        }, 3000);
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 0 20px;
  height: 280px;
  text-align: center;
  .title-container {
    font-size: 20px;
    text-align: center;
    height: 32px;
    line-height: 32px;
    margin-bottom: 15px;
    letter-spacing: 2px;
  }
  .qrcode img {
    width: 180px;
    height: 180px;
  }
}
</style>
