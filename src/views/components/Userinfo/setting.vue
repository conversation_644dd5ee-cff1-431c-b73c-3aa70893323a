<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      :title="'设置' | lang"
      :visible="true"
      width="500px"
      :before-close="closeForm"
    >
      <div style="padding: 0 20px;">
        <el-tabs v-model="tabName" @tab-click="switchTab">
          <el-tab-pane :label="'编辑资料' | lang" name="info">
            <div class="tab-body">
              <el-form :model="form" label-position="left" label-width="80px">
                <el-form-item :label="'头像' | lang" prop="avatar">
                  <el-upload
                    class="user-avatar"
                    action=""
                    :before-upload="uploadCheck"
                    :http-request="uploadImage"
                    :show-file-list="false"
                    :multiple="false"
                  >
                    <img :src="form.avatar || '/static/img/no_avatar.png'" style="height: 50px; width: 50px; float: left;" />
                  </el-upload>
                </el-form-item>
                <el-form-item :label="'昵称' | lang" prop="nickname">
                  <el-input
                    v-model="form.nickname"
                    :placeholder="'请输入昵称' | lang"
                    type="text"
                    style="width: 280px;"
                  />
                </el-form-item>
              </el-form>
              <div style="display: flex; justify-content: center; align-items: center; margin-top: 40px;">
                <el-button type="default" icon="el-icon-close" size="small" @click="closeForm">{{ '取 消' | lang }}</el-button>
                <el-button type="primary" icon="el-icon-check" size="small" @click="doSubmit">{{ '保存修改' | lang }}</el-button>
              </div>

            </div>
          </el-tab-pane>
          <el-tab-pane :label="'账号管理' | lang" name="account">
            <div class="tab-body">
              <div class="menus">
                <div v-if="login_phone" class="item" @click="showBindPhone">
                  <div>
                    <svg-icon class="icon" icon-class="ic_phone" />
                    <span class="text-grey">{{ '手机账号' | lang }}</span>
                  </div>
                  <div>
                    <a class="text-primary" v-if="phone">{{ '已绑定' | lang }}（{{ phone.substr(0,3) + "****" + phone.substr(7) }}）</a>
                    <a class="text-danger" v-else>{{ '未绑定' | lang }}</a>
                  </div>
                </div>
                <div v-if="login_wechat" class="item" @click="showBindWechat">
                  <div>
                    <svg-icon class="icon" icon-class="ic_wechat" />
                    <span class="text-grey">{{ '微信账号' | lang }}</span>
                  </div>
                  <div>
                    <a class="text-primary" v-if="openid">{{ '已绑定' | lang }}</a>
                    <a class="text-danger" v-else>{{ '未绑定' | lang }}</a>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <bindPhone v-if="bindPhoneShow" :phone="phone" @success="getAccounts" @close="closeBindPhone" />
    <bindWechat v-if="bindWechatShow" @success="getAccounts" @close="closeBindWechat" />
  </div>
</template>

<script>
import { setUserInfo, getAccounts } from '@/api/user'
import { uploadImage } from '@/api/upload'
import bindPhone from './bindPhone'
import bindWechat from './bindWechat'

export default {
  name: 'editUserInfo',
  components: { bindPhone, bindWechat },
  props: {
    avatar: {
      type: String,
      default: ''
    },
    nickname: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tabName: 'info',
      form: null,
      phone: '',
      openid: '',
      bindPhoneShow: false,
      bindWechatShow: false
    }
  },
  computed: {
    login_phone() {
      return this.$store.state.user.login_phone
    },
    login_wechat() {
      return this.$store.state.user.login_wechat
    }
  },
  created() {
    this.form = {
      avatar: this.avatar,
      nickname: this.nickname
    }
  },
  methods: {
    closeForm() {
      this.$emit('close')
    },
    switchTab() {
      if (this.tabName === 'account') {
        this.getAccounts()
      }
    },
    getAccounts() {
      getAccounts().then(res => {
        this.phone = res.data.phone
        this.openid = res.data.openid
      })
    },
    showBindPhone() {
      this.bindPhoneShow = true
    },
    closeBindPhone() {
      this.bindPhoneShow = false
    },
    showBindWechat() {
      this.bindWechatShow = true
    },
    closeBindWechat() {
      this.bindWechatShow = false
    },
    doSubmit(type) {
      setUserInfo(this.form).then(res => {
        this.closeForm()
        this.$message.success(res.message)
        this.$emit('success')
      })
    },
    uploadImage(item) {
      var form = new FormData()
      form.append('image', item.file)
      if (item.data) {
        form.append('data', JSON.stringify(item.data))
      }
      uploadImage(form).then(res => {
        if (!res.data.path) {
          this.$message.error('上传失败')
        }
        this.$set(this.form, 'avatar', res.data.path)
      })
    },
    uploadCheck(field) {
      var ext = ''
      var arr = field.name.split('.')
      if (arr.length > 1) {
        ext = arr[arr.length - 1].toLowerCase()
      }
      if (!['png', 'jpg', 'jpeg', 'gif'].includes(ext)) {
        this.$message.error('不支持的图片格式');
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.tab-body {
  padding: 20px;
  height: 240px;
}
.menus {
  background: #f8f8f8;
  border-radius: 10px;
  .item {
    width: 100%;
    padding: 15px 15px;
    border-bottom: 1px solid #f3f6f9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    color: #666;
    box-sizing: border-box;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    border-radius: 4px;
    .icon {
      margin-right: 10px;
      font-size: 18px;
    }
    a {
      font-size: 14px;
    }
    &:hover {
      background: #eff0f0;
    }
    &:last-child {
      border-bottom: 0;
    }
  }
}
</style>
