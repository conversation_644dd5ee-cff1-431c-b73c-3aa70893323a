<template>
  <div class="tools-item" :class="{disabled: !hasUndo}" @click="editor.history.undo()">
    <el-tooltip content="撤销">
      <a><svg-icon class="icon" icon-class="ic_mind_undo" /></a>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    editor: {
      type: Object,
      default: null
    },
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      hasUndo: false
    }
  },
  mounted() {
    const _this = this
    this.minder.on('datachange contentchange', function() {
      _this.hasUndo = _this.editor.history.hasUndo()
    })
  }
}
</script>
