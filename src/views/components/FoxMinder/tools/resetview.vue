<template>
  <el-tooltip content="调整视野到中心位置">
    <div class="tools-item">
      <a @click="minder.execCommand('camera', minder.getSelectedNode() || minder.getRoot(), 600)">
        <svg-icon class="icon" icon-class="ic_mind_resetview" />
      </a>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  methods: {
    isDisabled: function(command) {
      return this.minder.queryCommandState(command) === -1
    }
  }
}
</script>
