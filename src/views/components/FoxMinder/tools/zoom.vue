<template>
  <el-dropdown
    trigger="click"
    placement="bottom"
  >
    <div class="tools-item" style="width: 52px;" @dblclick="setZoom(100)">
      <span class="el-dropdown-link">
        <a>{{ zoom }}%</a>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-slider :min="10" :max="200" vertical style="height: 150px;" :value="zoom" :show-tooltip="false" :marks="{100: ''}" @input="setZoom" />
      </el-dropdown-menu>
    </div>
  </el-dropdown>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      zoom: 100
    }
  },
  mounted() {
    const _this = this
    this.minder.setDefaultOptions({ zoom: [10, 20, 40, 60, 80, 100, 120, 140, 160, 180, 200] })
    this.minder.on('zoom', function(e) {
      _this.zoom = e.zoom
    })
  },
  methods: {
    setZoom(val) {
      const zoom = val
      this.zoom = zoom
      this.minder.execCommand('zoom', zoom)
    }
  }
}
</script>
