<template>
  <div style="display: flex; align-items: center;">
    <div class="tools-item" :class="{disabled: isDisabled('fontfamily')}" style="background: transparent; padding-right: 3px;">
      <el-select v-model="font" :disabled="isDisabled('fontfamily')" style="width: 100px;" size="mini" clearable placeholder="字体">
        <el-option
          v-for="item in familyList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="size" :disabled="isDisabled('fontsize')" style="width: 65px; margin-left: 8px;" size="mini" clearable placeholder="字号">
        <el-option
          v-for="sizeItem in sizeList"
          :key="sizeItem"
          :label="sizeItem"
          :value="sizeItem"
        />
      </el-select>
    </div>
    <div style="display: flex; align-items: center;">
      <el-link class="tools-item" :class="{active: minder.queryCommandState('italic') === 1, disabled: isDisabled('italic')}" :disabled="isDisabled('italic')" :underline="false" @click="italicChange">
        <svg-icon class="icon" icon-class="ic_mind_fontstyle" />
      </el-link>
      <el-link class="tools-item" :class="{active: minder.queryCommandState('bold') === 1, disabled: isDisabled('bold')}" :disabled="isDisabled('bold')" :underline="false" @click="boldChange">
        <svg-icon class="icon" icon-class="ic_mind_fontweight" />
      </el-link>
      <el-popover
        placement="bottom"
        trigger="click"
        :disabled="isDisabled('forecolor')"
      >
        <colorPicker v-model="color" />
        <el-link slot="reference" class="tools-item" :class="{disabled: isDisabled('forecolor')}" :disabled="isDisabled('forecolor')" :underline="false">
          <svg-icon class="icon" icon-class="ic_mind_fontcolor" :style="'color:' + minder.queryCommandValue('forecolor')" />
        </el-link>
      </el-popover>
      <el-popover
        placement="bottom"
        trigger="click"
        :disabled="isDisabled('background')"
      >
        <color-picker v-model="backgroundColor" />
        <el-link slot="reference" class="tools-item" :class="{disabled: isDisabled('background')}" :disabled="isDisabled('background')" :underline="false">
          <svg-icon class="icon" icon-class="ic_mind_bgcolor" :style="'color:' + minder.queryCommandValue('background')" />
        </el-link>
      </el-popover>
    </div>
  </div>
</template>

<script>
import { Sketch } from 'vue-color'
export default {
  components: {
    colorPicker: Sketch
  },
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      familyList: [{
        label: '宋体',
        value: '宋体,SimSun'
      }, {
        label: '微软雅黑',
        value: '微软雅黑,Microsoft YaHei'
      }, {
        label: '楷体',
        value: '楷体,楷体_GB2312,SimKai'
      }, {
        label: '黑体',
        value: '黑体, SimHei'
      }, {
        label: '隶书',
        value: '隶书, SimLi'
      }, {
        label: 'Andale Mono',
        value: 'andale mono'
      }, {
        label: 'Arial',
        value: 'arial,helvetica,sans-serif'
      }, {
        label: 'arialBlack',
        value: 'arial black,avant garde'
      }, {
        label: 'Comic Sans Ms',
        value: 'comic sans ms'
      }, {
        label: 'Impact',
        value: 'impact,chicago'
      }, {
        label: 'Times New Roman',
        value: 'times new roman'
      }, {
        label: 'Sans-Serif',
        value: 'sans-serif'
      }],
      font: this.minder.queryCommandValue('fontfamily') || '微软雅黑,Microsoft YaHei',
      sizeList: [10, 12, 16, 18, 24, 32, 48],
      size: this.minder.queryCommandValue('fontsize') || 16,

      color: { hex: this.minder.queryCommandValue('forecolor') },
      backgroundColor: { hex: this.minder.queryCommandValue('background') }
    }
  },
  watch: {
    font: function() {
      this.minder.execCommand('fontfamily', this.font)
    },
    size: function() {
      this.minder.execCommand('fontsize', this.size)
    },
    'color.hex': function() {
      this.minder.execCommand('forecolor', this.color.hex)
    },
    'backgroundColor.hex': function() {
      this.minder.execCommand('background', this.backgroundColor.hex)
    }
  },
  mounted() {
    this.minder.on('contentchange', () => this.$forceUpdate())
  },
  methods: {
    isDisabled: function(command) {
      return this.minder.queryCommandState(command) === -1
    },
    italicChange: function() {
      this.minder.execCommand('italic')
      this.$forceUpdate()
    },
    boldChange: function() {
      this.minder.execCommand('bold')
      this.$forceUpdate()
    }
  }
}
</script>
