<template>
  <div class="km-btn-group">
    <el-row>
      <el-link :disabled="isDisabled('AppendChildNode')" :underline="false" type="primary" @click="minder.execCommand('AppendChildNode', '分支主题')"><i class="el-icon-bottom-right" /> 插入下级</el-link>
      <el-link :disabled="isDisabled('AppendParentNode')" :underline="false" type="primary" @click="minder.execCommand('AppendParentNode', '分支主题')"><i class="el-icon-top-left" /> 插入上级</el-link>
    </el-row>

    <el-row>
      <el-link :disabled="isDisabled('AppendSiblingNode')" :underline="false" type="primary" @click="minder.execCommand('AppendSiblingNode', '分支主题')"><i class="el-icon-refresh" /> 插入同级</el-link>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  methods: {
    isDisabled: function(command) {
      return this.minder.queryCommandState(command) === -1
    }
  }
}
</script>
