<template>
  <div style="display: flex; align-items: center">
    <el-dropdown
      placement="bottom"
      trigger="click"
      @command="handleCommand"
    >
      <div class="tools-item">
        <span class="el-dropdown-link">
          <el-tooltip content="配色">
            <a class="color-block" :style="'background-color: ' + themes[curName.replace('-compat', '')].color" />
          </el-tooltip>
        </span>
        <el-dropdown-menu slot="dropdown">
          <div class="theme-list">
            <el-dropdown-item v-for="name in Object.keys(themes)" :command="name">
              <a :style="'background-color: ' + themes[name].color" class="theme-item">{{ themes[name].title }}</a>
            </el-dropdown-item>
          </div>
        </el-dropdown-menu>
      </div>
    </el-dropdown>
    <div class="tools-item" :class="{active: curName.indexOf('-compat') !== -1}" @click="switchCompat">
      <el-tooltip content="紧凑布局">
        <a>紧凑</a>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      themes: {
        // 'classic': '脑图经典',
        // 'classic-compact': '紧凑经典',
        // 'snow': '温柔冷光',
        // 'snow-compact': '紧凑冷光',
        // 'fish': '鱼骨图',
        // 'wire': '线框',
        'fresh-red': {
          title: '红',
          color: '#bf7373'
        },
        'fresh-soil': {
          title: '黄',
          color: '#bf9373'
        },
        'fresh-green': {
          title: '绿',
          color: '#73bf76'
        },
        'fresh-blue': {
          title: '蓝',
          color: '#73a1bf'
        },
        'fresh-purple': {
          title: '紫',
          color: '#7b73bf'
        },
        'fresh-pink': {
          title: '粉',
          color: '#bf7394'
        }
        // 'fresh-red-compat': '紧凑红',
        // 'fresh-soil-compat': '紧凑黄',
        // 'fresh-green-compat': '紧凑绿',
        // 'fresh-blue-compat': '紧凑蓝',
        // 'fresh-purple-compat': '紧凑紫',
        // 'fresh-pink-compat': '紧凑粉',

        // 'tianpan': '圆盘',
        // 'tianpan-compact': '紧凑天盘'
      },
      curName: 'fresh-green'
    }
  },
  computed: {
    thumbStyle() {
      const theme = this.themes[this.curName.replace('-compat', '')]
      return {
        'color': theme['color'],
        'border-radius': '10px',
        'background-color': theme['color']
      }
    },
    isCompat() {
      return this.curName.indexOf('-compat') !== -1
    }
  },
  mounted() {
    this.curName = this.minder.queryCommandValue('theme') || 'fresh-green'
  },
  methods: {
    handleCommand(name) {
      if (this.isCompat) {
        name += '-compat'
      }
      this.curName = name
      this.minder.execCommand('theme', this.curName)
    },
    switchCompat() {
      if (this.isCompat) {
        this.curName = this.curName.replace('-compat', '')
      } else {
        this.curName += '-compat'
      }
      this.minder.execCommand('theme', this.curName)
    }
  }
}
</script>
<style lang="scss" scoped>
.tool-item a.thumb {
  display: block;
  width: 22px;
  height: 18px;
  border-radius: 4px;
}
.theme-list {
  width: 80px;
  text-align: center;
  min-width: auto;
  .theme-item {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: #fff;
    border-radius: 4px;
    margin: 0;
  }
}
</style>
