<template>
  <div class="tools-item" :class="{disabled: !hasRedo}" @click="editor.history.redo()">
    <el-tooltip content="重做">
      <a><svg-icon class="icon" icon-class="ic_mind_redo" /></a>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    editor: {
      type: Object,
      default: null
    },
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      hasRedo: false
    }
  },
  mounted() {
    const _this = this
    this.minder.on('datachange contentchange', function() {
      _this.hasRedo = _this.editor.history.hasRedo()
    })
  }
}
</script>
