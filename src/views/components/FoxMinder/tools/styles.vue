<template>
  <div class="km-btn-group">
    <el-row>
      <el-link :disabled="isDisabled('copystyle')" :underline="false" type="primary" @click="minder.execCommand('copystyle')">
        <i class="el-icon-copy-document" /> 复制样式</el-link>

      <el-link :disabled="isDisabled('clearstyle')" :underline="false" type="primary" @click="minder.execCommand('clearstyle')">
        <i class="el-icon-delete-solid" /> 清空样式</el-link>
    </el-row>
    <el-row>
      <el-link :disabled="isDisabled('pastestyle')" :underline="false" type="primary" @click="minder.execCommand('pastestyle')">
        <i class="el-icon-document" /> 粘贴样式</el-link>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  mounted() {
    this.minder.on('contentchange', () => this.$forceUpdate())
  },
  methods: {
    isDisabled: function(command) {
      return this.minder.queryCommandState(command) === -1
    }
  }
}
</script>
