<template>
  <div class="fox-mind">
    <div ref="container" class="minder-container" />
    <div v-if="minder" class="minder-tools">
      <drag :minder.sync="minder" />
      <zoom :minder.sync="minder" />
      <undo :minder.sync="minder" :editor.sync="editor" />
      <redo :minder.sync="minder" :editor.sync="editor" />
      <template-dropdown :minder.sync="minder" />
      <theme :minder.sync="minder" />
      <div class="split" />
      <resetview :minder.sync="minder" />
      <expand :minder.sync="minder" />
      <pick :minder.sync="minder" />
      <div class="split" />
      <fonts :minder.sync="minder" />
      <div class="split" />
      <export-data :minder.sync="minder" />
    </div>
    <ai :minder.sync="minder" />
  </div>
</template>

<script>
import 'hotbox-ui'
import 'kity'
import 'kityminder-core'
import 'kityminder-core/dist/kityminder.core.css'
import 'kityminder-editor-plugin'
import 'kityminder-editor-plugin/src/style.css'
// import 'hotbox-ui/hotbox.css'
import ai from './ai'
import { undo, redo, templateDropdown, theme, resetview, expand, pick, fonts, drag, zoom, exportData } from './tools/index'

export default {
  components: {
    undo, redo, templateDropdown, theme, resetview, expand, pick, fonts, drag, zoom, exportData, ai
  },

  data() {
    return {
      minder: null,
      editor: null,
      aiInputShow: false
    }
  },
  mounted() {
    this.editor = new window.kityminder.Editor(this.$refs.container)
    this.minder = window.minder = this.editor.minder
    this.minder.execCommand('theme', 'fresh-green')
    this.minder.execCommand('template', 'default')
  },
  methods: {
    showAiInput() {
      this.aiInputShow = true
    }
  }
}
</script>

<style lang="scss">
  .minder-container {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent !important;
    z-index: 1;
  }
  .minder-tools {
    width: 950px;
    position: absolute;
    z-index: 2;
    background: #f7f7f8;
    border-radius: 10px;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    display: flex;
    align-items: center;
    padding: 4px 9px;
    box-shadow: 0 0 20px rgba(100, 100, 100, 0.1);
    .split {
      height: 14px;
      width: 1px;
      background: #ddd;
      margin: 0 10px;
      padding: 0;
    }
    .tools-item {
      padding: 4px 8px;
      border-radius: 5px;
      color: #666 !important;
      margin: 0 3px;
      font-size: 14px;
      cursor: pointer;
      .icon {
        font-size: 18px;
      }
      .color-block {
        display: block;
        width: 16px;
        height: 16px;
        border-radius: 4px;
      }
      &:hover, &:active {
        background: #eff0f0;
      }
      &.active {
        background: #e3e4e4;
      }
      &.disabled {
        color: #ccc !important;
        .icon {
          color: #ccc !important;
        }
      }
      &.btn-primary {
        //background: linear-gradient(108deg, #a561ff 0, #ff6060 96%), linear-gradient(69deg, #ffa100 14%, #f95757 49%) !important;
        background: linear-gradient(108deg, #35b1d9 0, #1adca9 96%) !important;
        color: #fff !important;
        &:hover {
          background: linear-gradient(108deg, #289dc2 0, #15bb8f 96%) !important;
        }
      }
    }
  }
  .el-popover {
    padding: 0;
  }
</style>
