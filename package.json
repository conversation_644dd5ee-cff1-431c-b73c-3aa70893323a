{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@traptitech/markdown-it-katex": "^3.6.0", "axios": "0.18.1", "core-js": "^3.23.2", "element-ui": "2.15.9", "file-saver": "^2.0.1", "highlight.js": "^11.11.1", "hotbox-ui": "^1.0.0", "html-docx-js": "0.3.1", "js-base64": "^3.7.5", "js-cookie": "2.2.0", "jszip": "3.10.1", "katex": "^0.16.4", "kity": "^2.0.4", "kityminder-core": "^1.4.50", "kityminder-editor-plugin": "^0.0.1", "markdown-it": "^13.0.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "regenerator-runtime": "^0.13.11", "svg-baker-runtime": "^1.4.7", "vue": "2.6.10", "vue-clipboard2": "0.3.3", "vue-color": "^2.8.1", "vue-qr": "^4.0.9", "vue-router": "3.5.1", "vue-style-loader": "^4.1.3", "vuex": "3.1.0", "xlsx": "^0.14.1"}, "devDependencies": {"@types/katex": "^0.16.0", "@types/markdown-it": "^12.2.3", "@types/markdown-it-link-attributes": "^3.0.1", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "echarts": "4.8.0", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "less": "^4.1.3", "less-loader": "^11.1.0", "markdown-it-link-attributes": "^4.0.1", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}